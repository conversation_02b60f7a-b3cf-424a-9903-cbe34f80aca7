# api/app.py
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import joblib
import pandas as pd
import numpy as np
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))
from swahili_preprocessor import SwahiliPreprocessor

app = Flask(__name__)
CORS(app)

# Global variables for model and preprocessor
model = None
feature_extractor = None
preprocessor = None

def load_model():
    """Load the trained model and preprocessor"""
    global model, feature_extractor, preprocessor
    
    try:
        # Load model
        model = joblib.load('models/best_model.pkl')
        print("✅ Model loaded successfully")
        
        # Load feature extractor
        try:
            feature_extractor = joblib.load('models/feature_extractor.pkl')
            print("✅ Feature extractor loaded successfully")
        except:
            print("⚠️ Feature extractor not found, creating new instance")
            from feature_extractor import SwahiliFeatureExtractor
            feature_extractor = SwahiliFeatureExtractor()
        
        # Initialize preprocessor
        preprocessor = SwahiliPreprocessor()
        print("✅ Preprocessor initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def extract_features_for_prediction(message):
    """Extract features for a single message"""
    try:
        # Extract linguistic features
        features = preprocessor.extract_basic_features(message)
        
        # Load training feature columns to ensure consistency
        training_features = pd.read_csv('data/processed/features.csv')
        
        # Create feature DataFrame
        feature_df = pd.DataFrame([features])
        
        # Ensure all training columns are present
        # Get missing columns
        missing_cols = [col for col in training_features.columns if col not in feature_df.columns]

        # Add all missing columns at once using pd.concat
        if missing_cols:
            missing_df = pd.DataFrame(0, index=feature_df.index, columns=missing_cols)
            feature_df = pd.concat([feature_df, missing_df], axis=1)
        
        # Reorder columns to match training data
        feature_df = feature_df[training_features.columns]
        
        return feature_df
        
    except Exception as e:
        print(f"Error extracting features: {e}")
        return None

@app.route('/')
def home():
    """Home page with simple interface"""
    html_template = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🛡️ Swahili Scam Detector</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            }
            h1 {
                color: #2c3e50;
                text-align: center;
                margin-bottom: 10px;
            }
            .subtitle {
                text-align: center;
                color: #7f8c8d;
                margin-bottom: 30px;
            }
            textarea {
                width: 100%;
                height: 120px;
                padding: 15px;
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                font-size: 16px;
                resize: vertical;
                box-sizing: border-box;
            }
            button {
                background: #667eea;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                font-size: 16px;
                cursor: pointer;
                width: 100%;
                margin-top: 15px;
            }
            button:hover {
                background: #5a6fd8;
            }
            .result {
                margin-top: 20px;
                padding: 20px;
                border-radius: 10px;
                display: none;
            }
            .scam {
                background: #fee;
                border: 2px solid #dc3545;
                color: #721c24;
            }
            .safe {
                background: #efe;
                border: 2px solid #28a745;
                color: #155724;
            }
            .examples {
                margin-top: 30px;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
            }
            .example {
                background: white;
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
                cursor: pointer;
                border-left: 4px solid #667eea;
            }
            .example:hover {
                background: #f0f0f0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🛡️ Swahili Scam Detector</h1>
            <p class="subtitle">Kinga ya Ujumbe wa Udanganyifu</p>
            
            <textarea id="messageInput" placeholder="Andika ujumbe hapa / Enter message here...
Mfano: Hongera! Umeshinda TSH 1,000,000. Piga *123# kuconfirm..."></textarea>
            
            <button onclick="checkMessage()">🔍 Angalia Ujumbe / Check Message</button>
            
            <div id="result" class="result">
                <h3 id="resultTitle"></h3>
                <p id="resultText"></p>
                <p id="confidence"></p>
            </div>
        </div>

        <script>
            async function checkMessage() {
                const message = document.getElementById('messageInput').value.trim();
                
                if (!message) {
                    alert('Tafadhali andika ujumbe kwanza / Please enter a message first');
                    return;
                }
                
                try {
                    const response = await fetch('/api/predict', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ message: message })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        displayResult(result);
                    } else {
                        alert('Kuna hitilafu: ' + result.error);
                    }
                    
                } catch (error) {
                    alert('Kuna hitilafu imetokea: ' + error.message);
                }
            }
            
            function displayResult(result) {
                const resultDiv = document.getElementById('result');
                const resultTitle = document.getElementById('resultTitle');
                const resultText = document.getElementById('resultText');
                const confidence = document.getElementById('confidence');
                
                if (result.is_scam) {
                    resultDiv.className = 'result scam';
                    resultTitle.textContent = '⚠️ Ujumbe wa Udanganyifu / Scam Message Detected!';
                    resultText.textContent = 'Tahadhari! Ujumbe huu unaonekana kuwa wa udanganyifu.';
                } else {
                    resultDiv.className = 'result safe';
                    resultTitle.textContent = '✅ Ujumbe Salama / Safe Message';
                    resultText.textContent = 'Ujumbe huu unaonekana kuwa salama.';
                }
                
                confidence.textContent = `Uhakika / Confidence: ${(result.confidence * 100).toFixed(1)}%`;
                resultDiv.style.display = 'block';
            }
            
            function useExample(element) {
                document.getElementById('messageInput').value = element.textContent.trim();
            }
        </script>
    </body>
    </html>
    """
    return render_template_string(html_template)

@app.route('/api/predict', methods=['POST'])
def predict_scam():
    """API endpoint for scam prediction"""
    try:
        # Get message from request
        data = request.json
        message = data.get('message', '')
        
        if not message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Check if model is loaded
        if model is None:
            return jsonify({'error': 'Model not loaded'}), 500
        
        # Extract features
        features = extract_features_for_prediction(message)
        
        if features is None:
            return jsonify({'error': 'Feature extraction failed'}), 500
        
        # Make prediction
        prediction = model.predict(features)[0]
        probability = model.predict_proba(features)[0]
        
        # Get confidence (max probability)
        confidence = max(probability)
        
        # Prepare response
        response = {
            'message': message,
            'is_scam': bool(prediction == 1),
            'confidence': float(confidence),
            'scam_probability': float(probability[1]),
            'safe_probability': float(probability[0])
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': f'Prediction failed: {str(e)}'}), 500

@app.route('/api/status')
def status():
    """API status endpoint"""
    return jsonify({
        'status': 'running',
        'model_loaded': model is not None,
        'version': '1.0.0'
    })

@app.route('/api/test')
def test_api():
    """Test endpoint with sample predictions"""
    test_messages = [
        "Hongera! Umeshinda TSH 1,000,000. Piga *150*00# sasa kuconfirm.",
        "Habari za asubuhi. Mkutano wetu ni saa 2 jioni leo.",
        "URGENT! Tuma pesa haraka kwa namba hii 0712345678.",
        "Asante kwa huduma nzuri. Tuonane kesho asubuhi."
    ]
    
    results = []
    for message in test_messages:
        try:
            features = extract_features_for_prediction(message)
            if features is not None:
                prediction = model.predict(features)[0]
                probability = model.predict_proba(features)[0]
                
                results.append({
                    'message': message,
                    'is_scam': bool(prediction == 1),
                    'confidence': float(max(probability))
                })
            else:
                results.append({
                    'message': message,
                    'error': 'Feature extraction failed'
                })
        except Exception as e:
            results.append({
                'message': message,
                'error': str(e)
            })
    
    return jsonify({
        'test_results': results,
        'total_tests': len(test_messages)
    })

if __name__ == '__main__':
    print("🚀 Starting Swahili Scam Detection API...")
    print("=" * 50)
    
    # Load model
    if load_model():
        print("✅ All components loaded successfully!")
        print("\n🌐 Starting web server...")
        print("📱 Open your browser and go to: http://localhost:5000")
        print("🔗 API endpoint: http://localhost:5000/api/predict")
        print("🧪 Test endpoint: http://localhost:5000/api/test")
        print("\n⏹️  Press Ctrl+C to stop the server")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to load model. Please run model training first.")
        print("💡 Run: python src/model_trainer.py")