# install_dependencies.py
import subprocess
import sys
import os

def run_command(command):
    """Run a command and handle errors"""
    try:
        print(f"🔄 Running: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ Success: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running: {command}")
        print(f"Error output: {e.stderr}")
        return False

def install_dependencies():
    """Install all required dependencies for Swahili Scam Detection"""
    
    print("🚀 SWAHILI SCAM DETECTION - DEPENDENCY INSTALLER")
    print("=" * 60)
    print("Installing all required libraries...")
    print()
    
    # Core Python packages
    core_packages = [
        "pandas",
        "numpy", 
        "scikit-learn",
        "matplotlib",
        "seaborn",
        "plotly",
        "nltk",
        "beautifulsoup4",
        "requests",
        "flask",
        "fastapi",
        "uvicorn",
        "jupyter",
        "notebook"
    ]
    
    # Deep Learning packages
    dl_packages = [
        "torch",
        "torchvision", 
        "torchaudio",
        "transformers",
        "accelerate>=0.26.0"
    ]
    
    # Optional packages
    optional_packages = [
        "spacy",
        "tensorflow",
        "keras"
    ]
    
    print("1️⃣ INSTALLING CORE PACKAGES")
    print("-" * 30)
    for package in core_packages:
        run_command(f"pip install {package}")
    
    print("\n2️⃣ INSTALLING DEEP LEARNING PACKAGES")
    print("-" * 40)
    for package in dl_packages:
        run_command(f"pip install {package}")
    
    print("\n3️⃣ INSTALLING TRANSFORMERS WITH TORCH SUPPORT")
    print("-" * 50)
    run_command("pip install transformers[torch]")
    
    print("\n4️⃣ INSTALLING OPTIONAL PACKAGES")
    print("-" * 35)
    for package in optional_packages:
        success = run_command(f"pip install {package}")
        if not success:
            print(f"⚠️  Optional package {package} failed - continuing...")
    
    print("\n5️⃣ DOWNLOADING NLTK DATA")
    print("-" * 30)
    try:
        import nltk
        nltk.download('punkt')
        nltk.download('stopwords')
        print("✅ NLTK data downloaded")
    except Exception as e:
        print(f"⚠️  NLTK download failed: {e}")
    
    print("\n6️⃣ VERIFICATION")
    print("-" * 20)
    
    # Test critical imports
    critical_imports = [
        ("pandas", "pd"),
        ("numpy", "np"),
        ("sklearn", "sklearn"),
        ("torch", "torch"),
        ("transformers", "transformers"),
        ("flask", "flask"),
        ("matplotlib.pyplot", "plt")
    ]
    
    failed_imports = []
    
    for module, alias in critical_imports:
        try:
            exec(f"import {module} as {alias}")
            print(f"✅ {module} - OK")
        except ImportError as e:
            print(f"❌ {module} - FAILED")
            failed_imports.append(module)
    
    print("\n" + "=" * 60)
    
    if not failed_imports:
        print("🎉 ALL DEPENDENCIES INSTALLED SUCCESSFULLY!")
        print("✅ Your system is ready for Swahili Scam Detection research")
    else:
        print("⚠️  SOME DEPENDENCIES FAILED:")
        for module in failed_imports:
            print(f"   - {module}")
        print("\nTry running the failed packages manually:")
        for module in failed_imports:
            print(f"   pip install {module}")
    
    print("\n📋 NEXT STEPS:")
    print("1. Navigate to your project directory")
    print("2. Run: python data/sample_data.py")
    print("3. Run: python src/model_trainer.py")
    print("4. Run: python src/bilstm_model.py")
    print("5. Run: python src/model_comparison.py")

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ required. Please upgrade Python.")
        return False
    else:
        print("✅ Python version compatible")
        return True

def main():
    """Main installation function"""
    print("🔍 SYSTEM CHECK")
    print("-" * 20)
    
    if not check_python_version():
        return
    
    print(f"📁 Current directory: {os.getcwd()}")
    print()
    
    # Ask for confirmation
    response = input("Do you want to install all dependencies? (y/n): ").lower()
    
    if response in ['y', 'yes']:
        install_dependencies()
    else:
        print("Installation cancelled.")

if __name__ == "__main__":
    main()