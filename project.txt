NATURAL LANGUAGE PROCESSING BASED APPROACH FOR DETECTING <PERSON><PERSON><PERSON><PERSON><PERSON> SCAM MESSAGES THROUGH MACHINE LEARNING MODELS IN TANZANIA
A Case Study of Tanzania Police Force








BLASTUS BEATUS LIHAWA



Master of Technology in Computing and Communication Dissertation Report

Dar es Salaam Institute of Technology

JUNE 2025
 
NATURAL LANGUAGE PROCESSING BASED APPROACH FOR DETECTING SWAH<PERSON>I SCAM MESSAGES THROUGH MACHINE LEARNING MODELS IN TANZANIA
A Case Study of Tanzania Police Force







BY
BLASTUS BEATUS LIHAWA


A Dissertation Submitted in (Partial) Fulfillment of the Requirement for the Degree of Master of Technology in Computing and Communication of the Dar es Salaam Institute of Technology

Dar es Salaam Institute of Technology

JUNE 2025

 
CERTIFICATION
We, the undersigned, certify that we have read and hereby recommend for acceptance by the Dar es Salaam Institute of Technology this dissertation entitled: "Natural Language Processing-Based Approach for Detecting Swahili Scam Messages Through Machine Learning Model in Tanzania: A Case Study of Tanzania Police Force," submitted in partial fulfillment of the requirements for the award of the degree of Master of Technology in Computing and Communication of the Dar es Salaam Institute of Technology..

Dr. <PERSON><PERSON><PERSON>
(Main Supervisor)

Date: -----------------------------------


Dr. Gaudence Tesha
(Co Supervisor)

Date: -----------------------------------

	 
 
DECLARATION AND COPY RIGHT
I, Blastus Lihawa, declare that this dissertation is my own original work and that it has not been submitted to any other institution for the award of a similar or any other academic qualification.

Signature				

This dissertation is a copyright-protected material under the Berne Convention, the Copyright Act of 1999, and other applicable international and national laws on intellectual property. No part of this work may be reproduced, stored in a retrieval system, or transmitted in any form or by any means—electronic, mechanical, photocopying, recording, or otherwise—without prior written permission from the Deputy Rector of Academic, Research and Consultancy, acting on behalf of both the author and the Dar es Salaam Institute of Technology, except for brief excerpts used for research or private study with proper acknowledgment.











	ACKNOWLEDGEMENT
I would like to express my sincere gratitude to my supervisors, Dr. Othmar Mwambe and Dr. Gaudence Tesha, for their invaluable guidance, support, and encouragement throughout this research. Their expertise and insightful feedback were instrumental in shaping the direction and quality of this study titled "Natural Language Processing-Based Approach for Detecting Swahili Scam Messages through Machine Learning Model in Tanzania: A Case Study of Tanzania Police Force."
My heartfelt thanks also go to my family, friends, and colleagues for their unwavering support, patience, and motivation throughout this academic journey. Their encouragement gave me the strength to overcome challenges and remain focused.
I gratefully acknowledge the contributions of all researchers whose prior work laid the foundation for this study, and I extend my appreciation to the Dar es Salaam Institute of Technology for providing the resources, academic environment, and institutional support necessary for conducting this research.
Lastly, I extend special thanks to all individuals and organizations, including members of the Tanzania Police Force, who directly or indirectly contributed to the successful completion of this dissertation.

 
DEDICATION
This dissertation is dedicated to my beloved family, whose unwavering love, patience, and support have been the cornerstone of my academic journey. Their constant encouragement and belief in my potential have given me the strength and motivation to undertake and complete this work.
I am deeply grateful for the countless sacrifices they have made and the steadfast support they have shown, which have profoundly contributed to both my academic and personal growth. Their selflessness and enduring faith in my abilities continue to inspire me as I pursue this research.
To all individuals committed to creating a positive impact through technology may this work serve as a meaningful step toward building safer and more inclusive digital environment

 
LIST OF ABBREVIATIONS
AI – 			Artificial Intelligence
API – 			Application Programming Interface
DL-			Deep Learning
ICT – 			Information and Communication Technology
IDS-			Intrusion Detection System
IMO-			International Marinetime Organization
IoT-			Internet of Things
ML – 			Machine Learning
NLP – 			Natural Language Processing
RF -			Random Forest
SMiShing – 		SMS Phishing
SMS – 		Short Message Service
SPAM – 		Unsolicited Bulk Messages
TF-IDF		Term Frequency –Inverse Document Frequency
URL – 		Uniform Resource Locator

 
ABSTRACT
Cyber security threats—particularly scam messages—have emerged as a serious challenge in digital and mobile communication, often leading to financial losses and violations of personal privacy. While numerous detection models have been developed for high-resource languages like English, there remains a critical gap in tools designed for Kiswahili, the most widely spoken language in Tanzania and across East Africa.
This study, titled "Natural Language Processing-Based Approach for Detecting Swahili Scam Messages Through Machine Learning Model in Tanzania: A Case Study of Tanzania Police Force," proposes a Machine Learning (ML)-based solution enhanced by Natural Language Processing (NLP) techniques to detect scam messages written in Kiswahili. The research emphasizes the analysis of linguistic patterns and contextual features specific to Kiswahili, integrating customized preprocessing techniques such as stop-word removal, slang normalization, and typo correction to improve detection accuracy.
By addressing the complexities of low-resource language processing, this study contributes to strengthening cyber security efforts in Tanzania. The findings are expected to support the Tanzania Police Force, policymakers, telecommunication companies, and cyber security practitioners in identifying and mitigating digital fraud. Additionally, the study advances NLP research for underrepresented languages, fostering the development of inclusive and context-aware cyber security solutions.
.
 
TABLE OF CONTENTS
CERTIFICATION	i
DECLARATION AND COPY RIGHT	ii
ACKNOWLEDGEMENT	iii
DEDICATION	iv
LIST OF ABBREVIATIONS	v
ABSTRACT	vi
LIST OF FIGURES	x
LIST OF TABLES	x
CHAPTER ONE INTRODUCTION	1
1.1 Background of the Study	1
1.2 Problem Statement	2
1.3 Research Objectives	3
1.4 Research Questions	3
1.5 Significance of the Study	3
1.6 Scope of the Study	4
1.8 Major Contributions of the Study	5
CHAPTER TWO LITERATURE REVIEW	7
2.1 Introduction	7
2.2 Cybersecurity and Scam Detection: Insights from Existing Research	7
2.2.1 Scam Detection Using NLP and ML Approaches	7
2.2.2 Maritime Cybersecurity and Hybrid Threats	8
2.2.3 The Role of Detection and Response Time in Cybersecurity	8
2.2.4 IoT Security and Phishing Attacks	9
2.3 Challenges in Kiswahili NLP and Scam Detection	9
2.4 Theoretical Framework	10
2.4.1 System Block Diagram	11
2.5 Research Gaps and Opportunities	13
2.6 Literature Summary	14
CHAPTER THREE  METHODOLOGY	15
3.1 Research Design	15
3.2 Data Collection	15
3.3 Data Preprocessing	16
3.4 Model Development	17
3.4.1 Traditional Machine Learning Models:	17
3.4.2 Deep Learning Models	18
3.5 Model Evaluation and Validation	20
3.6 Deployment Strategy	21
3.4. Expected Outcomes	21
3.5 Methodology Matrix Table	22
CHAPTER FOUR:  DATA PRESENTATION AND ANALYSIS	24
4.1 Introduction	24
4.2 Demographic Characteristics of Respondents	24
4.3 Insights into Kiswahili Scam Messages	28
4.3.1 Common Features in Scam Messages	29
4.4 Perception Toward Machine Learning Solutions	30
4.5 Correlation with Model Evaluation	31
4.6 Deployment Considerations	32
4.7 Summary of the Findings	33
CHAPTER FIVE: MODEL DEVELOPMENT	34
5.1 Introduction	34
5.2 Dataset Preparation	34
5.3 Data Preprocessing	36
5.4 Feature Engineering	37
5.5 Model Architecture and Training	40
5.6 Model Evaluation	42
CHAPTER SIX:  SYSTEM DEVELOPMENT	45
6.1 Introduction	45
6.2 System Architecture	45
6.3 Technology Stack	46
6.4 Backend System Code (Flask API)	47
6.5 Sample User Interface (Mobile App)	48
6.6 Real-Time Deployment Strategy	49
6.7 Security and Privacy Considerations	50
6.8 Usability Testing and Feedback	50
CHAPTER SEVEN:  DISCUSSION OF FINDINGS	52
7.1 Introduction	52
7.2 Discussion Based on Research Objectives	52
7.2.1 Linguistic Characteristics of Kiswahili Scam Messages	52
7.2.2 Model Development and Effectiveness	52
7.2.3 Stakeholder Perceptions and Real-World Application	53
7.2.4 Dataset Contribution	53
7.3 Alignment with Theoretical Framework	53
CHAPTER EIGHT:  SUMMARY, CONCLUSION, AND RECOMMENDATIONS	54
8.1 Summary of the Study	54
8.2 Conclusion	54
8.3 Recommendations	55
REFERENCES	56
APPENDEX	58
APPENDIX I: RESEARCH TIME FRAME	59
APPENDEX II: RESEARCH BUDGET	60
APPENDEX III: RESEARCH QUESTIONNAIRE	61
	

 
LIST OF FIGURES
Figure 1 - 2.1:System Block Diagram	11
Figure 2 - 4.1:Gender Distribution Chart	26
Figure 3 -4.2:Age group Chart	26
Figure 4 - 4.3:Occupation Distribution Chart	27
Figure 5 - 4.4:Experience Level Distribution Chart	27
Figure 6 - 5.1: Code on Preprocessed data	37
Figure 7 - 5.2: Code SVM Training	40
Figure 8 - 5.3: Code Random Forest	41
Figure 9 - 5.4: Code Bi-LSTM	41
Figure 10 - 5.5:Code Swahili BERT	42
Figure 11 - 5.6: Model performance Comparison	44

LIST OF TABLES
Table 1 - 3.1: Methodology Matrix Table	22
Table 2 - 4.1:Scam Type	28
Table 3 - 4.2: Message Features	29
Table 4 - 4.3:Belief in ML as a Scam Detection Tool	30
Table 5 - 4.4: Preferred ML Techniques for Scam Detection	30
Table 6 - 4.5: Models Results	31
Table 7 - 5.1: Model Evaluation	44
Table 8:Research Time Frame	59
Table 9; Research Budget	60


 
CHAPTER ONE
INTRODUCTION
1.1 Background of the Study
In recent years, cybersecurity threats have become increasingly prevalent worldwide, with scam messages especially those delivered via text and online platforms—emerging as a major concern. These messages often aim to trick recipients into disclosing personal or financial information, leading to identity theft, privacy infringements, and financial loss. In Tanzania, the rapid growth of mobile communication technologies and mobile money services has further intensified this threat. Kiswahili, being the dominant language in digital communications across the country, adds a unique dimension to the problem.
NLP, when integrated with ML, offers a powerful method for detecting scam messages by analyzing language features and contextual patterns. However, while significant progress has been made in building scam detection models for English texts, Kiswahili remains underrepresented in such efforts. As a low-resource language in NLP, Kiswahili suffers from a lack of annotated datasets, standardized linguistic tools, and tailored machine learning models.
Previous research has explored aspects of Kiswahili language processing. For instance, [1] investigated offensive language detection using applied Apache Spark and ML methods to detect offensive language, while [2] another developed a hybrid model combining TF-IDF and feature selection for identifying SMS phishing ("smashing") in Kiswahili. Despite these advancements, the focus remains limited, especially in detecting scam messages targeting mobile money users—a growing vulnerability in Tanzania and much of Sub-Saharan Africa.
Compounding the challenge is the use of slang, abbreviations, and informal language common in Kiswahili scam messages, which complicates traditional detection methods. This situation underscores the urgent need for context-aware, language-specific cybersecurity solutions capable of accurately identifying scam messages written in Kiswahili. This study aims to fill this critical gap by designing and implementing an NLP and ML-based approach tailored specifically for detecting Kiswahili scam messages. Through an in-depth analysis of Swahili linguistic structures and the application of appropriate machine learning techniques, the research seeks to enhance digital safety for Tanzanian users particularly within the framework of the Tanzania Police Force’s cybersecurity operations.
1.2 Problem Statement
Despite the increasing prevalence of scam messages in Tanzania, there remains a significant lack of automated detection systems specifically designed for Kiswahili. Most existing models are optimized for English-language content, making them ineffective in identifying the nuances of scam messages written in Kiswahili. As a result, millions of Tanzanian mobile and online users especially those using mobile money platforms are left vulnerable to fraud, identity theft, and data breaches.
Kiswahili's unique linguistic structure and context-specific expressions further limit the effectiveness of English-based detection models. Moreover, the absence of annotated datasets and specialized NLP tools for Kiswahili has constrained the development of reliable scam detection systems.
This study responds to this gap by developing a NLP and ML based model specifically designed to detect scam messages in Kiswahili. Focusing on the Tanzanian context, particularly within the Tanzania Police Force, the research aims to strengthen efforts in digital fraud prevention and contribute to national cybersecurity resilience
1.3 Research Objectives
The main objective of this study is to design and evaluate an NLP-driven ML model for the detection of scam messages in Kiswahili. Specifically, the study seeks to:
	Analyze the linguistic characteristics and structures common in Kiswahili scam messages.
	Develop a machine learning-based NLP model tailored to detect scam messages written in Kiswahili.
	Evaluate the performance of the developed model using accuracy and other performance metrics.
	Construct and annotate a Swahili-language dataset for use in scam detection and related NLP tasks.
1.4 Research Questions
This study is guided by the following research questions:
	What are the distinctive linguistic and contextual features of scam messages written in Kiswahili?
	How can machine learning techniques be applied to effectively classify Kiswahili scam messages?
	 What is the accuracy, precision, recall, and overall effectiveness of the developed model?
	How can the developed model be deployed in real-world cybersecurity systems in Tanzania?
1.5 Significance of the Study
This research has both theoretical and practical significance:
	Enhancing Cybersecurity for Swahili Speakers: The model developed through this study it has offer a practical tool for detecting fraudulent messages, thereby reducing users’ susceptibility to digital scams and financial fraud.
	Informing Policy and Regulation: Findings from this study can support regulatory bodies and government agencies in crafting effective cybersecurity policies and digital safety strategies.
	Empowering Telecom and Financial Service Providers: Telecommunications companies and mobile money service providers can integrate the detection model into their infrastructure to proactively filter scam messages and secure user interactions.
	Advancing Cybersecurity Expertise: Cybersecurity professionals gained valuable insights into developing threat detection systems tailored to under-resourced languages and regions.
	Contributing to NLP Research in Low-Resource Languages: By developing a labeled Swahili dataset and NLP models, this research contributes to the growing field of computational linguistics focused on African languages, particularly those with limited digital representation.
1.6 Scope of the Study
This study focuses on scam messages written in Kiswahili, particularly those transmitted through SMS and mobile messaging platforms commonly used in Tanzania. The research is limited to the detection of fraudulent text messages and does not extend to voice-based or image-based scams. The model was trained and tested on messages collected within the Tanzanian context and evaluated using standard ML performance metrics.
1.8 Major Contributions of the Study
This study is expected to make several significant contributions in both theoretical and practical dimensions:
	Development of a Kiswahili Specific Scam Detection Model; The research   result in the creation of a machine learning model specifically designed to detect scam messages written in Kiswahili. Unlike generic models trained on English data, this solution incorporated linguistic and cultural nuances unique to Tanzanian digital communication.
	Construction of a Labeled Kiswahili Scam Message Dataset: One of the major outcomes of the study is the compilation and annotation of a novel dataset comprising genuine and fraudulent Kiswahili messages. This resource was instrumental for future NLP research in Swahili and other low-resource languages.
	Advancement of NLP for Low-Resource Languages: By addressing the scarcity of NLP tools and datasets for Kiswahili, the study contributes to the broader effort to extend computational linguistics capabilities to underrepresented languages, enabling more equitable access to AI-driven cybersecurity solutions.
	Integration of Linguistic Preprocessing Techniques: The study was introduced and evaluate advanced text preprocessing strategies tailored to Kiswahili, such as slang normalization, typo correction, and stop-word removal, which are critical for improving classification accuracy in real-world scenarios.
	Practical Application Framework for Cybersecurity: The findings and developed model offered a blueprint for integrating scam detection into real-world systems, such as mobile telecom platforms and financial services, thereby enhancing protection for millions of Swahili-speaking users.
	Academic Contribution to Cross-Disciplinary Research: This research bridges the fields of cybersecurity, linguistics, and artificial intelligence, demonstrating the interdisciplinary value of applying machine learning to language-specific problems in digital security.
 
CHAPTER TWO
LITERATURE REVIEW
2.1 Introduction
Cybersecurity has become an essential pillar in protecting digital communication systems from evolving threats such as scam messages, phishing attacks, and data breaches. Among these, scam messages designed to deceive individuals into revealing sensitive information pose a significant risk to personal privacy and financial security. Traditional detection approaches, such as rule-based systems, have shown limitations in scalability and adaptability. Recent advancements in NLP and ML offer promising alternatives by enabling automated detection and classification of scam content through linguistic and contextual analysis [3]. This chapter presents a comprehensive review of relevant literature focusing on scam detection, cybersecurity advancements, and the integration of NLP and ML techniques, with a special focus on challenges and research gaps in Swahili-language applications.
2.2 Cybersecurity and Scam Detection: Insights from Existing Research
The global rise in digital communication has led to an increase in cyber threats targeting critical sectors such as finance, transportation, and public communication. With the proliferation of mobile-based services in Sub-Saharan Africa, particularly mobile money platforms, there is an urgent need for context-aware scam detection systems. This section examines studies across different domains where NLP and ML have been successfully applied for scam detection and cyber threat mitigation.
2.2.1 Scam Detection Using NLP and ML Approaches
Scam detection using NLP involves analyzing linguistic cues such as tone, syntax, word choice, and semantics to identify fraudulent messages[4]. Machine Learning models, particularly supervised classifiers such as SVM, RF, and DL models, have been employed to distinguish scam from legitimate content [5]. These models have outperformed traditional keyword-based approaches in terms of precision, recall, and adaptability. NLP tools extract features like n-grams, sentiment scores, and TF-IDF vectors, which are then used by ML classifiers to make predictions. However, challenges such as language bias, lack of multilingual datasets, and vulnerability to adversarial inputs continue to impact their reliability and generalizability.
2.2.2 Maritime Cybersecurity and Hybrid Threats
Maritime cybersecurity is becoming increasingly critical due to the digitization of navigation, cargo handling, and communication systems aboard vessels. [6] Highlights vulnerabilities in Southeast Asian maritime corridors where outdated IT infrastructure, untrained crew, and weak governance increase susceptibility to cyber-physical attacks.
Hybrid threats combining physical sabotage with digital intrusion can disrupt global supply chains. While the IMO has introduced cybersecurity guidelines, enforcement and adaptation remain inconsistent. Suggested mitigations include the establishment of cyber incident reporting platforms, crew training programs, and enhanced risk assessment protocols for onboard digital systems.
2.2.3 The Role of Detection and Response Time in Cybersecurity
The timeliness of detecting and responding to cyber threats greatly affects an organization’s resilience. According to [7], the effectiveness of IDS and phishing detectors is heavily dependent on minimizing latency in threat recognition. ML algorithms, including anomaly detection models, have significantly reduced detection times. Nonetheless, achieving real-time detection remains a challenge due to computational overhead and the dynamic nature of cyber threats. Incorporating automated response systems with real-time threat intelligence can further improve system responsiveness and user protection.
2.2.4 IoT Security and Phishing Attacks
The proliferation of IoT devices has opened new avenues for phishing and scam-related attacks. Smart homes, wearable devices, and autonomous vehicles are all susceptible to intrusions via social engineering tactics. The STRIDE framework (Spoofing, Tampering, Repudiation, Information Disclosure, Denial of Service, and Elevation of Privilege) has been employed to classify and mitigate these threats [6].Integration of NLP-driven anomaly detection and secure authentication mechanisms is critical to fortifying IoT infrastructure, especially in sectors like healthcare and energy, where breaches can have severe consequences.
2.3 Challenges in Kiswahili NLP and Scam Detection
Despite notable progress in NLP and scam detection research for high-resource languages like English, Swahili presents distinct challenges that hinder the development of effective detection models:
	Limited Annotated Datasets: Scarcity of labeled scam messages in Swahili hampers the training of supervised learning models.
	Lack of Pre-trained NLP Models: Most state-of-the-art NLP libraries (e.g., spaCy, BERT) lack robust Swahili versions or embeddings.
	Code-switching and Informality: Scam messages often contain a mix of Swahili, English, slang, and abbreviations, making normalization and preprocessing difficult.
	Morphological Complexity: As an agglutinative language, Swahili's words often have compound structures involving multiple prefixes and suffixes, complicating tasks like tokenization, stemming, and part-of-speech tagging.
Addressing these issues requires dedicated Swahili-language datasets, tokenizers, and preprocessing tools tailored to the Tanzanian context.
2.4 Theoretical Framework
The research is grounded in three interrelated theoretical perspectives:
	Information Processing Theory: Suggests that scam messages exploit cognitive biases in information intake and decision-making, making users susceptible to deception.
	Communication Theory: Provides insights into how language is used persuasively and manipulatively in scam communications.
	Supervised Learning Theory: Underpins the methodology for training ML models using labeled data to detect scam messages accurately and efficiently.
 
2.4.1 System Block Diagram
 
Figure 1 - 2.1:System Block Diagram
	User Input
Input Type: SMS or Text Message
The system initiates its operation by receiving a text message sent by a user. This message serves as the primary input and may contain various content in Swahili. The message is captured by the system through a designated interface, such as an SMS gateway or a mobile application.
	 Text Preprocessing Steps:
	Tokenization: The input text is segmented into individual tokens (words or terms) to enable easier analysis in subsequent stages.
	Stopword Removal:Commonly occurring but semantically insignificant words (e.g., "na", "ya", "kwa") are removed from the tokenized list to reduce noise and improve the focus on meaningful content.
	Lemmatization: Each word is reduced to its base or dictionary form (lemma). For example, Swahili words like “kupokea” might be lemmatized to “pokea.” This step standardizes word forms, enhancing the accuracy of feature representation.
The preprocessing stage transforms raw, unstructured text into a cleaner and more uniform format. This ensures that the input is suitable for feature extraction and reduces variability due to linguistic variations in the Swahili language.
	Feature Extraction Techniques:
	TF-IDF (Term Frequency–Inverse Document Frequency): This statistical measure evaluates how important a word is to a message relative to a larger corpus. It helps highlight keywords that are likely to indicate scam messages.
	Word Embeddings (e.g., Word2Vec, GloVe): Words are represented as dense vectors in a continuous vector space, capturing semantic relationships between words. This allows the system to understand context and similarity between terms even if they are not identical.
At this stage, the preprocessed text is transformed into a structured numerical format that can be understood by the machine learning model. This representation preserves the semantic and statistical features of the message content, enabling more accurate classification.
	Machine Learning Model
	Input: Numerical feature vectors obtained from the Feature Extraction step
	Model Type: Supervised learning model trained on a labeled dataset of Swahili scam and non-scam messages
The core of the system is a machine learning classifier that analyzes the extracted features. It has been trained on historical SMS data, where each message is labeled as either “Scam” or “Non-Scam.” Using this knowledge, the model predicts the label of incoming messages based on patterns it has learned.
	Classification Possible Output
	Scam
	Non-Scam
The machine learning model outputs a classification label that indicates whether the incoming message is potentially fraudulent ("Scam") or safe ("Non-Scam"). This result forms the basis for any subsequent action or alert.
	Alert and Reporting System (Optional Module)
	User Notification:If a message is classified as a scam, the system may display a warning notification to the user, cautioning them against interacting with the content.
	Message Blocking: In cases where automatic protection is enabled, the system can prevent the message from reaching the user's inbox or take other security measures, such as reporting the sender.
This optional module enhances user safety by taking proactive steps when a scam is detected. It adds a protective layer to the classification system, helping mitigate risks associated with SMS fraud and social engineering attacks.
2.5 Research Gaps and Opportunities
The review of current literature reveals several gaps and future research opportunities:
	Language Adaptation: Most detection models are trained in English, necessitating development of Swahili-specific NLP architectures such as Swahili-BERT or transformer-based models.
	Dataset Generation: There is a pressing need for the creation of annotated Swahili scam message corpora to enable the application of supervised learning.
	Real-world Integration: There is minimal evidence of NLP-based scam detectors being integrated into mobile money and telecom services, despite their potential impact.
	Hybrid Detection Models: Combining rule-based filters with ML classifiers can enhance detection accuracy, particularly in low-data environments.
2.6 Literature Summary
The literature appears to underscore what might be characterized as the dynamic nature of cyber threats and what seems to be the vital role of NLP and ML in addressing them. While what the evidence suggests is considerable progress in English-language scam detection, Swahili tends to remain underrepresented, both in terms of datasets and technological tools. What seems especially noteworthy in this analytical context is that maritime and IoT cybersecurity sectors appear to suggest the importance of timely detection, human training, and robust infrastructure—lessons that seemingly apply equally to mobile-based communication systems in Tanzania.

What this research appears to aim toward, therefore, is addressing these challenges by developing what might be characterized as a Kiswahili-specific scam detection model using NLP and ML approaches, which appears to contribute to cybersecurity enhancement and, within this broader analytical framework, what seems to constitute the evolving field of low-resource language processing. Given the complexity of these theoretical relationships, what appears particularly significant is the potential for contextual adaptation of existing methodologies to linguistically underserved communities. 
CHAPTER THREE
 METHODOLOGY
3.1 Research Design
This study employs a quantitative research design, with an emphasis on experimental methods to develop and evaluate a machine learning-based scam detection model tailored for the Swahili language. The design is structured to enable system investigation and measurable outcomes.
3.2 Data Collection
A high-quality, annotated dataset is foundational for any NLP-based classification task. This study built a Swahili-language scam message dataset using the following sources:
	Reports from Cyber security Agencies in Tanzania: Official records and digital scam reports was obtained from institutions such as the Tanzania Communications Regulatory Authority (TCRA) and other relevant cyber security agencies. These reports include structured data on message content, date, type of scam, and user complaints.
	User-submitted Messages via Mobile Network Providers: Telecom providers in Tanzania often maintain databases of scam SMSs reported by their customers. Permission was to be sought to access anonymized messages for academic research purposes. These messages are typically authentic, recent, and highly relevant.
	Web Scraping from Online Platforms: Automated scripts were used to collect scam messages from public forums, Swahili social media groups, and blogs discussing communication and ethical considerations such as respecting terms of service and user privacy was followed.
3.3 Data Preprocessing
Text data must be cleaned and transformed before being used for model training. The following preprocessing steps was applied:
	Text Cleaning: This includes:
Removal of URLs, email addresses, and phone numbers,
Elimination of emojis, punctuation, and special characters,
Conversion to lowercase,
Filtering of non-Swahili content using predefined wordlists or language identification tools.
	Tokenization: The cleaned messages was broken down into individual tokens (words or sub-words) using language-aware tokenizers (e.g., from Hugging Face or spaCy customized for Swahili).
	Stop word Removal: Common Swahili stop words such as "na", "ya", "kwa", etc., was removed to reduce noise. A curated list of Swahili stopwords was used, considering both standard and colloquial forms.
	Feature Extraction: Text was transformed into numerical representations using:
	TF-IDF (Term Frequency-Inverse Document Frequency): For capturing word relevance across the corpus,
	Word Embedding: Pre-trained vectors from Word2Vec or FastText models trained on Swahili corpora,
	Transformer-based Embedding: Using contextual embedding from Swahili-compatible models such as multilingual BERT (mBERT), XLM-RoBERTa, or fine-tuned Swahili BERT models.
3.4 Model Development
The significant about this research is that the core objective of this study is to be the development of predictive models that appear to be capable of accurately classifying Swahili-language scam messages. Within this broader analytical framework, the development process apparently involved both traditional machine learning and deep learning approaches. The evidence appears to reveal is that these models typically learn from a labeled dataset and tend to generalize to unseen data. Given the complex of these theoretical relationships, what these findings seem to point toward is that the outputs are predominantly evaluated based on what appears to represent probabilistic predictions or what seems to constitute classification scores.
3.4.1 Traditional Machine Learning Models:
Traditional machine learning models operate on engineered features, such as TF-IDF vectors or word embeddings, and use mathematical optimization techniques to learn patterns.
	Logistic Regression (LR): Is a linear classifier that models the probability that a given input vector x\mathbf{x}x belongs to class y=1y = 1y=1 (scam) using the sigmoid activation function:
P(y=1∣x)=σ(w⊤x+b)=1/+e−(w⊤x+b)					3.1
	X= Input feature vector (e.g., TF-IDF vector)
	w = Weight vector
	b = Bias term
	σ = Sigmoid function
The model is trained to minimize the binary cross-entropy loss:
Llog=−[y⋅log(y^)+(1−y)⋅log(1−y^)			3.2
	Support Vector Machine (SVM): Aims to find a hyperplane that maximizes the margin between two classes. The decision function is:
f(x)=sign(w⊤x+b)							3.3
SVM solves the optimization problem:
Minw,b1/2∥w∥2+C∑ max(0,1−yi(w⊤xi+b)) 		3.4
Where:
	C= Regularization parameter
	yi∈{−1,+1}yTrue class labels
	max(0,1−yi(⋅))= Hinge loss
	 Random Forest (RF): Is an ensemble of decision trees. Each tree is trained on a random subset of the data and features. The final prediction is made by  voting (classification):
y^=mode({ht(x)}t=1T)						3.5
Where:
	ht = Prediction of the ttt-th decision tree
	T=: Total number of trees in the forest
Random Forests reduce variance and are regularizing.
3.4.2 Deep Learning Models
Deep learning models automatically learn abstract representations from input data, particularly effective for sequential and contextual understanding in text classification.
	LSTM (Long Short-Term Memory): LSTM networks are a special kind of Recurrent Neural Network (RNN) capable of learning long-term dependencies. An LSTM cell uses the following equations:
Forget gate:
ft=σ(Wf⋅[ht−1,xt]+bf)f						3.6
	Input gate:
it=σ(Wi⋅[ht−1,xt]+bi),C t=tanh(WC⋅[ht−1,xt]+bC)	3.7
	Cell state update
Ct=ftʘCt-1+itʘĈt							3.8
	 Output gate:
ot=σ(Wo⋅[ht−1,xt]+bo),  	ht=Ot⊙tanh(Ct)		3.9
Where:
	⊙:= Element-wise multiplication
	Xt = Word embedding at time step t
	ht = Hidden state

	Bi-LSTM (Bidirectional LSTM): Processes the input sequence in both forward and backward directions, concatenating the outputs:
ht→,←ht=LSTM(xt)	⇒	ht=[ht→;←ht]			3.10
This allows the model to capture context from both preceding and succeeding words, improving semantic understanding.
	Transformer-based Models (BERT, XLM-R): Transformers use self-attention mechanisms to capture dependencies between words in a sentence, regardless of their distance.
The scaled dot-product attention mechanism is given by:
Attention(Q,K,V)=softmax(QK⊤/√dk)V			3.11
Where:
	Q,K,V= Query, Key, and Value matrices (from word embeddings)
	dk: Dimensionality of the key vectors
The BERT model computes embeddings for each token using multi-head attention and feed-forward layers, and the [CLS] token embedding is used for classification:
ŷ=softmax(W⋅h[CLS]+b)						3.12
Where:
	h[CLS]=: Final hidden state of the classification token
	W= Weight matrix for classification layer
	B = Bias term
BERT and XLM-R was fine-tuned using the Swahili scam message dataset to adapt the model to domain-specific vocabulary and context.
3.5 Model Evaluation and Validation
Robust evaluation techniques were used to ensure the generalizability and reliability of the models:
	Train-Test Split: The dataset was randomly divided into training (80%) and testing (20%) sets, maintaining class balance to prevent bias.
	K-Fold Cross-Validation: A 5-fold or 10-fold cross-validation technique was applied during training to assess model performance on unseen data. This helps in reducing over fitting and variance.
	Performance Metrics:
Accuracy: The result of Overall correctness of predictions.
Precision: What seems to constitute correct positive predictions out of all predicted positives (what appears particularly significant about these findings, as this metric seems to be especially important to avoid false alarms
Recall (Sensitivity): Result tends to suggest the proportion of actual scam messages correctly identified.
F1-Score: Harmonic mean of precision and recall, which seems to be especially valuable in what might be characterized as imbalanced datasets.
	Baseline Comparison: The performance of developed models was, within this broader analytical framework, compared against what appears to be simple keyword-based or rule-based classifiers typically used in mobile SMS filtering systems, to ostensibly demonstrate what these findings seem to point toward in terms of performance improvements.
3.6 Deployment Strategy
To ensure what appears to be practical applicability, what the deployment strategy seems to suggest was explored across what appears to represent multiple use-case integrations:
	Mobile Applications: It seems to constitute embedding the scam detection model in mobile applications to analyze incoming SMS and apparently alert users in real time.
	Telecom SMS Filtering Systems: This appears to indicate integration of the model into backend telecom systems for pre-filtering suspected scam messages before delivery to end user.
	Cyber security Platforms: It tends to suggest providing APIs or modules for use by law enforcement or cyber security experts for what appears to be forensic analysis.
	Usability Testing: The evidence appears to reveal is that selected end-users would participate in pilot tests. What seems to emerge from these findings is that feedback was gathered on model speed, accuracy, interpretability, and overall usefulness to presumably inform future iterations
3.7 Expected Outcomes
The study appears to tend to suggest what might be characterized as the following anticipated outcomes:
	 A curated and labeled dataset of Swahili-language scam and non-scam messages which seems to potentially constitute a resource that may be suitable for use by future researchers in what appears to be the fields of NLP and cyber security.
	 These considerations is a seemingly robust and ostensibly high-performing machine learning model that appears to be capable of identifying what might be characterized as scam messages with what tends to be high accuracy and predominantly low false-positive rates.
	 New insights into the linguistic and stylistic patterns used in Swahili scam communications, contributing to both academic and practical cyber security literature.
	 Practical recommendations for mobile operators, regulators, and security agencies in Tanzania for integrating AI-based scam detection into their operational workflows.
3.5 Methodology Matrix Table
Table 1 - 3.1: Methodology Matrix Table
S/N	Specific Objective	Data Collection Method	Data Collection Tools	Data Analysis	Deliverables
1	Analyze the linguistic characteristics and structures common in Kiswahili scam messages	Qualitative and Quantitative Analysis of message content	Scam messages from TCRA, telecoms, social media, user reports	Content analysis, N-gram analysis, POS tagging, slang/abbreviation patterns	Identified linguistic and stylistic features of Kiswahili scam messages
2	Develop a machine learning-based NLP model tailored to detect scam messages written in Kiswahili	Experimental setup using labeled dataset	Python, Jupyter Notebook, Scikit-learn, TensorFlow, Keras, mBERT/XLM-RoBERTa	Supervised model training (Logistic Regression, SVM, RF, LSTM, BERT); hyperparameter tuning	ML/NLP model trained for Kiswahili scam detection
3	Evaluate the performance of the developed model using accuracy and other performance metrics	Model testing and cross-validation	Confusion matrix, Scikit-learn metrics, 5-fold cross-validation	Accuracy, Precision, Recall, F1-Score, ROC Curve, Baseline comparison	Evaluation report showing model performance with benchmark comparison
4	Construct and annotate a Swahili-language dataset for use in scam detection and related NLP tasks	Manual and automated dataset compilation and labeling	Data scraping tools (e.g., BeautifulSoup), Label Studio, Excel, regex filters, Swahili linguistic experts	Annotation schema design, verification, labeling consistency checks	A labeled and balanced dataset of Swahili scam and non-scam messages, ready for NLP applications

 
CHAPTER FOUR:
 DATA PRESENTATION AND ANALYSIS
4.1 Introduction
This chapter presents a comprehensive analysis of the research findings based on data collected through structured questionnaires and model evaluation. The aim was to assess the perceptions of key stakeholders especially from the Tanzania Police Force on Kiswahili scam messages, and to validate the applicability of ML models for detection. The data was analyzed both quantitatively and qualitatively, with the support of visual tools such as charts and tables, and is aligned with the study’s objectives.
4.2 Demographic Characteristics of Respondents
The demographic data appears to suggest is that the study involved what seems to be a diverse group of 65 respondents, with what appears of being a gender distribution where males comprised the majority at approximately 61.5%, followed by females at around 30.8%, and about 7.7% choosing not to disclose their gender. The age distribution was ostensibly fairly balanced, with what seems to emerge from these findings is that the largest group was aged 26–35 (roughly 38.5%), suggesting what appears to represent a youthful and potentially tech-savvy population. Occupationally, within this broader analytical framework, police officers made up what appears to be the largest segment (approximately 38.5%), which tends to align with the study's focus on the Tanzania Police Force, followed by IT experts (around 23.1%), cybersecurity analysts and academics (about 15.4% each), and a small percentage from other fields (seemingly 7.7%). What the evidence appears to reveal is that experience levels were varied, with nearly half (presumably 46.1%) having more than three years of experience, which appears to provide evidence that may support the reliability of the insights gathered. This spread across demographics, given the complexity of these theoretical relationships, appears to lend support to what may represent the robustness of the findings by seemingly ensuring representation from key stakeholders in cybersecurity and law enforcement. See table 4.1 and Fig 4.1, 4.2, 4.3 and 4.4 below.
Attribute	Categories	Frequency	Percentage (%)
Gender	Male	40	61.5
	Female	20	30.8
	Prefer not to say	5	7.7
Age Group	18–25	10	15.4
	26–35	25	38.5
	36–45	20	30.8
	46 and above	10	15.4
Occupation	Police Officer	25	38.5
	Cybersecurity Analyst	10	15.4
	IT Expert	15	23.1
	Academic/Researcher	10	15.4
	Other	5	7.7
Experience	None	5	7.7
	Less than 1 year	10	15.4
	1–3 years	20	30.8
	More than 3 years	30	46.1
 
Figure 2 - 4.1:Gender Distribution Chart
 
Figure 3 -4.2:Age group Chart

 
Figure 4 - 4.3:Occupation Distribution Chart

 
Figure 5 - 4.4:Experience Level Distribution Chart
 
4.3 Insights into Kiswahili Scam Messages
Based on the responses gathered, mobile money fraud emerged as the most common type of Kiswahili scam message, reported by 76.9% of participants. This high frequency appears to reflect what seems to be the widespread use of mobile financial services in Tanzania, which tends to make them what might be characterized as prime targets for scammers. Lottery or prize scams were the second most reported, cited by approximately 69.2% of respondents, suggesting what appears to be the continued popularity of fraudulent messages promising cash or material winnings as a tactic to lure victims. What seems in this analytical context is that fake job offers were also understand (around 46.2%), often seemingly used to exploit high unemployment rates and job-seeking individuals. Within this broader analytical framework, impersonation scams, where attackers ostensibly pose as authorities such as police officers, accounted for what appears to be 38.5% of reports, highlighting what tends to indicate a tactic that appears to leverage social trust and fear. What the evidence seems to suggest is that romance or relationship scams were experienced by roughly 30.8% of respondents, revealing what appears to represent a manipulative strategy that typically targets emotional vulnerabilities. What these findings seem to point toward, given the multifaceted nature of this evidence, is the varied and seemingly sophisticated nature of Kiswahili scam messages, which appears to reinforce what might be characterized as the need for targeted awareness and technical interventions countermeasures. Please see the Table 4.1 below
Table 2 - 4.1:Scam Type
Scam Type	Frequency	Percentage (%)
Mobile money fraud	50	76.9
Lottery or prize scams	45	69.2
Fake job offers	30	46.2
Impersonation (e.g. police)	25	38.5
Romance/relationship scams	20	30.8
4.3.1 Common Features in Scam Messages
The data seems to suggest is that requests for personal information (approximately 69.2%) appear to be predominant, where scammers ostensibly aim to extract personal data such as ID numbers, PINs, or what seems to constitute mobile money credentials. What appears particularly significant about these findings is that urgency/threats (around 61.5%) represent common tactics that typically include "act now or lose it" threats that apparently induce panic. Poor grammar/misspellings (roughly 53.8%) tends to emerge as theoretically important as a hallmark of many scams, possibly due to translation or what appears to be deliberate targeting of less alert individuals. What these findings seem to point toward is that promises of gifts (about 46.2%), such as what appears to represent enticing offers like cash prizes or free airtime, are seemingly used to bait victims. Additionally, considering the nuanced nature of these findings, religious/emotional appeals (approximately 38.5%) in messages that invoke God, family, or health issues appear to be employed to elicit what presumably constitutes empathy from potential victims. Please see the Table 4.2 below.


Table 3 - 4.2: Message Features
Message Feature	Frequency	Percentage (%)
Requests for personal info	45	69.2
Urgency or threats	40	61.5
Poor grammar or misspellings	35	53.8
Promises of rewards or gifts	30	46.2
Religious/emotional appeals	25	38.5
 
4.4 Perception Toward Machine Learning Solutions
4.4.1 Belief in ML as a Scam Detection Tool
A large majority (84.6%) believe machine learning (ML) can be used effectively to detect and combat scam messages. This shows strong public confidence in AI-driven solutions to cybercrime.
Table 4 - 4.3: Belief in ML as a Scam Detection Tool
Response	Frequency	Percentage (%)
Yes	55	84.6
No	5	7.7
Not Sure	5	7.7

4.4.2 Preferred ML Techniques for Scam Detection
	Deep Learning (LSTM) was the most preferred method, likely due to its strong performance in natural language understanding.
	Random Forest and SVM are also recognized, possibly due to their wide use in academic and practical ML tasks.
	Not familiar (15.4%) highlights the need for public education on AI techniques.
Table 5 - 4.4: Preferred ML Techniques for Scam Detection
Technique	Frequency	Percentage (%)
Deep Learning (LSTM)	25	38.5
Random Forest	15	23.1
SVM	10	15.4
Naïve Bayes	5	7.7
Not familiar	10	15.4
4.5 Correlation with Model Evaluation
The insights obtained from the questionnaire helped refine the features and metrics used in the model. Based on user emphasis on urgency, poor grammar, and keyword patterns, TF-IDF vectorization was employed for traditional ML models, and Swahili-BERT for contextual embedding.
4.5.1 ML Models Results Summary
Swahili-BERT delivered the best performance across all metrics. This is due to its deep understanding of Swahili syntax and semantics.
Bi-LSTM, also a strong performer, benefits from remembering sequences, helpful in sentence-level fraud detection.
Random Forest and SVM are good baselines but lack the contextual depth of deep learning methods.
Table 6 - 4.5: Models Results
Models	Accuracy	Precision	Recall	F1-Score
SVM	91.1%	92.0%	90.1%	91.0%
Random Forest	93.2%	93.6%	92.8%	93.2%
Bi-LSTM	94.2%	94.5%	94.1%	94.3%
Swahili-BERT	96.3%	96.5%	96.0%	96.2%
These models reflect the preferred methods mentioned by participants and demonstrate effective real-world application.
4.6 Deployment Considerations
During the survey, participants were asked to share their expectations and concerns regarding the deployment of a machine learning-based scam detection system. What the responses appear to suggest are several critical factors that seemingly need to be addressed to ensure what might be characterized as the system's effectiveness and usability in real-world scenarios:
	Accuracy and Reliability: What seems especially noteworthy in this analytical context is that the most frequently emphasized concern appears to be the system's ability to correctly identify scam messages without generating false positives or missing actual threats. Respondents tend to suggest what appears to be a strong belief that any solution deployed must deliver what seems to be consistently high levels of precision and recall to build what might be characterized as user trust and support law enforcement efforts.
	Ease of Use for Non-Technical Staff: Within this framework, many participants appear to highlight the need for a user friendly interface, particularly for frontline personnel such as police officers or community workers who may typically lack technical expertise. What this pattern seems to suggest, therefore, is the importance of designing what appears to be an intuitive dashboard that requires minimal training, that enabling swift interpretation and response to messages.
	User Privacy and Data Protection: What also appears significant in this context is ensuring the confidentiality and protection of users' personal information, which was apparently a major priority. Given the complexity of these theoretical relationships, participants expressed what appears to be substantial concerns about data misuse or breaches, which tends to signal that any deployed solution must presumably adhere strictly to privacy regulations and data encryption standards. What the evidence appears to reveal is that transparency in how data is processed and stored seems to constitute an essential element for public acceptance.
	Real-Time Detection Capability: What seems to emerge from these findings, within this broader analytical framework, is that timeliness in identifying and alerting authorities on scam messages was apparently another key requirement. Respondents seemingly advocated for what appears to represent a system that can analyze messages in real time and, considering the nuanced nature of these findings, immediately raise alerts for suspicious content, thereby ostensibly enabling what tends to suggest faster response and potential prevention of fraud in the majority of cases.
These insights appear to have directly informed what seems to constitute the deployment strategy of the developed solution. What the system appears to include:
	API Integration for what might be characterized as seamless embedding within existing messaging platforms and digital reporting tools.
	Mobile Compatibility to allow usage on phones and tablets, especially in what seems to be remote or field-based environments.
	A Simplified Alert Interface ostensibly tailored for law enforcement use, which tends to enable quick action upon detection of a suspected scam without seemingly requiring technical interpretation.
What appears particularly significant about these findings is that, by aligning the deployment approach with the needs and expectations of end users, the solution appears to be better positioned for what might represent real-world impact and adoption.
4.7 Summary of the Findings
What the findings from the questionnaire seem to suggest is that they tend to support the importance of what appears to be a localized and language-sensitive scam detection system. Within this  framework, the Swahili BERT model seems to align well with user expectations for accuracy and effectiveness, while what also appears significant in this context is that deep learning preference among respondents appears to further validate its deployment potential. Given the complexity of these theoretical relationships, these results appear to guide practical implementation within the Tanzania Police Force and other cybersecurity stakeholders.








CHAPTER FIVE:
MODEL DEVELOPMENT
5.1 Introduction
This chapter presents a comprehensive overview of the model development process undertaken to address the second research objective: “Develop a machine learning-based NLP model detect scam messages written in Kiswahili.” The development journey ensure several critical phases, including data preparation, feature extraction, model selection, training, hyperparameter tuning, and performance evaluation. By leveraging both traditional and deep learning approaches—such as Support Vector Machines, Random Forests, Bi-LSTM, and transformer-based models like Swahili-BERT—this chapter demonstrates how various models were trained and validated using a curated Swahili-language dataset. The goal was to achieve high accuracy, contextual understanding, and real-world applicability.
5.2 Dataset Preparation
	This source encompasses official scam message reports gathered from the Tanzania Communications Regulatory Authority (TCRA) and the Tanzania Police Force, which appears to tend to suggest what might be characterized as a reasonably comprehensive data foundation. Within this broader analytical framework, these institutions seem to regularly receive public complaints and ostensibly document fraudulent communication cases in the majority of instances. What seems especially noteworthy in this analytical context is that such data is presumably considered substantially reliable and predominantly representative of real scam threats encountered by Tanzanian users. What the evidence appears to reveal, given the multifaceted nature of this evidence, is that utilizing this data seems to lend support to what may represent a potentially more effective training approach. These reports typically include what appears to constitute message content, sender metadata, date received, and nature of the fraud attempt, which tends to suggest what seems to be their invaluable nature for what appears to indicate the training of what might be characterized as reasonably accurate machine learning models
	Reports from TCRA and the Tanzania Police Force
	User-reported messages via telecom networks
	Scraped messages from Swahili forums and social media
Each message in the dataset is labeled manually or semi-automatically into two distinct categories: Scam (1) and Non-Scam (0). Labeling is essential in supervised machine learning, where the model learns to differentiate between harmful and harmless messages based on these annotations. A scam message typically includes manipulation, deceit, or urgency intended to trick the recipient, while non-scam messages are legitimate texts like service updates or personal communication. Consistent labeling appears to tend to suggest what seems to be a necessary condition for models to learn what might be characterized as accurate patterns during the training process and make what appears to represent relatively reliable predictions during deployment.
The final compiled dataset contains approximately 5,000 labeled Kiswahili messages, representing what seems to constitute a mix of scam and non-scam examples. What appears particularly significant about these findings is that this sample size is substantially large for what appears to be a low-resource language like Kiswahili and, within this broader analytical framework, seems to provide what tends to suggest a good foundation for training and evaluating machine learning models. The evidence appears to show  that a dataset of this general allows for what appears to be meaningful statistical  and, considering the nuanced nature of these findings, tends to help prevent what seems to be exposing the model to that appears to indicate a wide range of message types and linguistic features.
What these findings seem to point toward is a dataset that appears to be divided into what seems to be two main subsets: approximately 80% for training and around 20% for testing. Given the complex of these theoretical relationships, this distribution tends to suggest what appears to be a standard machine learning practice that seemingly demonstrates appropriate allocation for both model development and subsequent evaluation. What also appears significant in this context is how this partitioning strategy appears to provide evidence that may support the creation of what might be characterized as robust predictive systems in what appears to warrant further interpretive consideration regarding low-resource language contexts. What the training set tends to be used for is teaching the model how to identify what appear to be patterns associated with what might be characterized as scam or non-scam messages. What seems  important in this findings context is that the testing set, which the model has ostensibly never encountered before, appears to be utilized to assess what seems to represent its performance and what appears to be its generalization capacity. This split tends to suggest what appears to be a standard practice in machine learning, seemingly ensuring that the model is not just predominantly memorizing the data but appears to demonstrate a capability of predicting new, unseen messages in what might be characterized as real-world situations.
5.3 Data Preprocessing
Lowercasing: What appears to constitute lowercasing refers to the process of converting all characters in the text to lowercase letters. For example, "Habari" seemingly becomes "habari". What this process appears to suggest, therefore, is uniformity in text data by treating words with different cases (e.g., "M-Pesa" vs. "m-pesa") as apparently the same. This approach tends to reduce what seems to be redundancy and appears to avoid creating separate tokens for the same word due to what appears to be capitalization differences. Given the complexity of these theoretical relationships, this seems particularly important in Swahili, where users may typically inconsistently capitalize words in SMS messages
Removal of Stop Words, Special Characters, and Emojis: What the evidence appears to reveal is that stop words are common words such as "na," "ya," or "kwa" in Kiswahili that apparently do not carry substantial meaning and can seemingly dilute the model's ability to learn useful patterns. Removing them appears to help reduce what might be characterized as noise. Similarly, special characters like @, #, or %, and emojis such as {😊 or 😡}, in the majority of cases do not appear to contribute to the semantic structure that seems necessary for scam detection. What these findings seem to point toward is that cleaning these out helps simplify the input and appears to improve computational efficiency, particularly for models that tend to rely on statistical patterns.
Slang Normalization and Spelling Correction: Within this broader analytical framework, in Kiswahili scam messages, informal language appears to be common—often written in what seems to be slang or with what appears to represent misspellings to presumably deceive filters. What the analysis tends to support is that slang normalization involves converting colloquial or abbreviated forms (e.g., "nipo" to "nimepo") to what appears to be standard Swahili. Spelling correction, considering the nuanced nature of these findings, seems to ensure words are matched correctly to known tokens during vectorization. This step appears to be vital for improving what seems to be model accuracy, especially in what appears to be a low-resource language like Kiswahili, where variation in spelling and style can apparently lead to reduced prediction quality if not properly addressed.
Tokenization using spaCy or Swahili NLP tokenizers appears to represent what might be characterized as the process of splitting text into smaller units such as words, subwords, or sentences. For example, what the sentence "Pesa zako zimepotea" would seemingly be broken down into tends to yield ["Pesa", "zako", "zimepotea"]. What seems particularly significant about these findings is that spaCy or Swahili-specific tokenizers are typically employed to handle what appears to be language-specific rules like agglutination and compound words in Swahili. Within this broader analytical framework, proper tokenization seems to generally indicate that the structure of the text is predominantly preserved and that words are ostensibly separated appropriately for what appears to be further processing, such as vectorization or embedding.
Lemmatization by considering the nuanced nature of these findings, tends to involve what appears to be the reduction of words to their base or root form, known as a lemma. What the evidence appears to reveal is that "ameshalipwa" (has already been paid) might presumably be lemmatized to "lipa" (to pay). What seems to emerge from these findings is that this step appears to help consolidate different grammatical forms of a word into what tends to suggest a standard form, apparently reducing feature space and seemingly improving the model's generalization. What appears to warrant further interpretive consideration, in light of these methodological considerations, is that in Kiswahili, where verbs and nouns apparently contain multiple affixes in the majority of cases, lemmatization seems to constitute a critical element for what appears to normalize input and what tends to ensure substantially consistent representation across messages with similar meaning but different structures.
 
Figure 6 - 5.1: Code on Preprocessed data
	Feature Engineering
5.4.1 TF-IDF Vectorization
Term Frequency–Inverse Document Frequency (TF-IDF) appears to represent what might be characterized as a widely used text representation technique in Natural Language Processing. What this approach seems to generally indicate is a method for converting text data into numerical values that tend to reflect how important a word appears to be in a message relative to a collection of messages (corpus). What seems especially noteworthy in this analytical context is that this transformation appears to be crucial because machine learning models cannot process raw text directly—they seemingly require numerical input.
Term Frequency (TF): This tends to measure how often a word appears in what might be characterized as a given document (in this case, a single SMS or message). What the evidence appears to reveal is that a higher frequency suggests the word may carry importance for that specific message. For example, within this broader analytical framework, if the word "cash" appears five times in one message, its TF is apparently higher compared to messages where it appears once.
Inverse Document Frequency (IDF): What this component seems to quantify is how rare a word tends to be across the majority of documents in the dataset. Words that occur in many messages (like "kwa" or "na" in Swahili) typically get lower scores, while rare words what appear to represent scam indicators like "bonyeza" (click) to get higher weights. What appears particularly significant about these findings is that this helps reduce the impact of common, non-informative words.
TF-IDF Value: The TF and IDF values are truely multiplied to generate what appears to be a TF-IDF score for each word in each message. What these findings seem to point toward is that this score is used to build a feature vector that tends to summarize the text numerically, prioritizing informative terms and seemingly reducing noise from common words.
What also appears significant in this context is its application in Traditional ML Models: Traditional machine learning algorithms like SVM and Random Forest apparently cannot process raw text. Given the complexity of these theory relationships, TF IDF appears to provide evidence that may support a simple yet effective way to convert text into fixed-length vectors, allowing these algorithms to presumably classify messages based on term importance.
Benefits in Scam Detection: What the analysis tends to support, considering the nuanced nature of these findings, is that TF-IDF helped highlight words that frequently appear in scam messages (like zawadi – prize or bonyeza  click), which seems to allow models to learn patterns and differentiate what appears to be scam from legitimate messages. What this pattern seems to suggest, therefore, is that this method boosts classification accuracy by focusing on what seem to be key linguistic features.
5.4.2 Word Embeddings
Pre-trained FastText for Swahili was used for deep learning: FastText is a word embedding technique developed by Facebook AI Research that represents words as vectors based on their character n-grams. This means it understands and encodes not only entire words but also sub word information like prefixes, suffixes, and roots. For Swahili rich language, FastText is especially useful because it can generate embeddings for rare or unseen words by analyzing their components. Using pre trained FastText vectors specific to Swahili enhances the models ability to understand the semantic relationships and contextual meanings of Swahili words, making it effective for downstream tasks like scam message.
Swahili BERT embeddings were obtained using transformers: Swahili BERT is a multilingual transformer-based model adapted to understand the Swahili language. BERT (Bidirectional Encoder Representations from Transformers) captures word meaning based on the context surrounding each word, both before and after it in a sentence. Unlike traditional embeddings, which typically assign a fixed vector to each word, what the evidence appears to reveal is that BERT produces dynamic embeddings that seem to vary depending on the word's usage. Given the complexity of these theoretical relationships, this appears to make Swahili-BERT particularly powerful for processing Kiswahili scam messages that apparently rely on informal, nuanced, or context-driven language. What appears to emerge from this evidence, within these evolving conceptual parameters, is that Swahili-BERT embeddings provided the model with what seems to constitute deep linguistic understanding, ostensibly enabling it to classify scam versus non-scam messages with substantial accuracy.
5.5 Model Architecture and Training
This section appears to describe what seems to be the structure and learning processes of the four selected models used for detecting Swahili scam messages. What the evidence tends to suggest is that each model was trained on preprocessed and labeled data to classify text messages as either scam or non-scam. The models were seemingly chosen based on what appears to be their ability to handle natural language classification tasks and were subsequently compared based on accuracy, precision, recall, and F1-score. What emerges from these findings is that four models were apparently developed and compared.
5.5.1 Support Vector Machine (SVM)
Support Vector Machine (SVM) appears to constitute a supervised learning algorithm that tends to find what seems to be the separating different classes in a high-dimensional space. What the investigation appears to indicate, given the complexity of these theoretical relationships, is that SVM was trained on TF-IDF vectorized message features to distinguish scam from non-scam messages. What appears particularly significant about these findings is that it seems to be effective for linearly separable data and appears to work well with small- to medium-sized datasets, which tends to suggest what appears to be a reliable baseline for comparison with more complex models.
 
Figure 7 - 5.2: Code SVM Training

5.5.2 Random Forest
Random Forest appears to be an ensemble learning algorithm that tends to combine multiple decision trees to make what seems to be predictions. Each tree is typically trained on what might be characterized as a random subset of the data, and their outputs are seemingly aggregated to improve accuracy and what appears to represent a reduction in overfitting. In this study, Random Forest was apparently used to classify messages based on engineered features, leveraging what seems to be its strength in handling what appears to be noisy or imbalanced datasets. What the evidence appears to reveal is that it tends to be known for what appears to be robustness and interpretability in classification tasks.

 
Figure 8 - 5.3: Code Random Forest

5.5.3 Bi-LSTM
Bi-LSTM appears to represent what might be characterized as a specialized type of recurrent neural network (RNN) that seems to be designed to capture sequential patterns in text. Unlike traditional RNNs, what tends to distinguish this architecture is how it processes input data in both forward and backward directions, which appears to enable it to learn context from what seems to be the surrounding words. Within the broader analytical framework of this research, Bi-LSTM was apparently employed to model what appears to be word order and semantic dependencies within Swahili messages, which seems to generally indicate improvements in scam detection accuracy by what appears to constitute an understanding of the full sentence structure rather than what might be characterized as isolated keywords—essentially what tends to function as a sequential deep learning model that appears to capture word order.
 
Figure 9 - 5.4: Code Bi-LSTM
5.5.4 Swahili-BERT (Transformer-based)
Swahili-BERT is a transformer-based model adapted from multilingual BERT (mBERT) and fine-tuned on Swahili scam message data. It uses self-attention mechanisms to understand contextual meaning and relationships between words across an entire text. This model excels use to capture complex language features and was the most accurate in this study. By leveraging pre trained knowledge and domain specific fine tuning, Swahili BERT provided super performance in detecting subtitle and context-sensitive scam indicators within Kiswahili texts.
 
Figure 10 - 5.5: Code Swahili BERT
5.6 Model Evaluation
Accuracy is the ratio of correctly predicted messages (both scam and non-scam) to the total number of messages analyzed. It gives an overall measure of how often the model makes correct predictions. In this classification, it is calculated as the sum of true positives and true negatives divided by the total number of predictions. While accuracy may be misleading when the dataset is imbalanced (i.e. one class dominates), as it may ignore class performance.
Formula:
Accuracy=(TP+TN)/(TP+TN+FP+FN)							 5.1
Precision seems to measure how many of the messages predicted as scam were, in the majority of cases, actually scams. What appears particularly significant about these findings is that it represents a key metric when the cost of false positives (wrongly classifying a legitimate message as a scam) appears to be high. What seems to emerge from these findings, within this broader analytical framework, is that high precision tends to suggest what appears to be a reliable model in flagging predominantly fraudulent messages, thus seemingly avoiding unnecessary alarms or blocking of non-scam content.

Formula:
Precision=  TP/(TP+FP)								5.2
Where:
TP = True Positives (correctly identified scams)
FP = False Positives (legitimate messages wrongly flagged)
Recall, also known as sensitivity or true positive rate, evaluates how many actual scam messages were correctly identified by the model. It is particularly important in contexts like fraud detection, where missing a scam message (false negative) can have severe consequences. A high recall means the model catches most of the scam messages, though it might also raise more false positives.
Formula:
Recall=  TP/(TP+FN)									5.3
The F1-Score is the harmonic mean of precision and recall, balancing both metrics to provide a single performance indicator. It is especially useful when the dataset is imbalanced, as it combines both the model's ability to avoid false positives and false negatives. A high F1-score indicates that the model performs well in identifying scams without missing many or wrongly classifying too many non-scams.
Formula:
F1 Score=2 X  (Precision x Recall)/(Precision+Recall)						5.4
Table 7 - 5.1: Model Evaluation
Model	Accuracy	Precision	Recall	F1-Score
SVM	91.1%	92.0%	90.1%	91.0%
Random Forest	93.2%	93.6%	92.8%	93.2%
Bi-LSTM	94.2%	94.5%	94.1%	94.3%
Swahili-BERT	96.3%	96.5%	96.0%	96.2%
Swahili-BERT emerged as the most effective model, combining contextual understanding with high accuracy. The results confirm that transformer-based models outperform traditional models in the Swahili context for scam message detection
 
Figure 11 - 5.6: Model performance Comparison




CHAPTER SIX: 
SYSTEM DEVELOPMENT
6.1 Introduction
This chapter presents what appears to be the practical implementation of the proposed Swahili scam message detection system, addressing what seems to constitute the research objective of deploying a machine learning model within a real-world application environment. Given the complexity of these theoretical relationships, it provides what appears to be a comprehensive overview of the system architecture, including input handling, text preprocessing, model inference, and alert mechanisms. What the analysis tends to support is that the chapter also seemingly describes the technology stack used for development, the user interface design for end-user interaction, and what appears to be a deployment strategy ostensibly tailored for law enforcement and telecom integration. What also appears significant in this context, considering the nuanced nature of these findings, are considerations for data security, real-time performance, and usability testing that seem to be discussed to ensure the system presumably meets operational expectations
6.2 System Architecture
The proposed scam detection system is structured into four modular layers to ensure scalability, real-time processing, and integration with law enforcement systems. Each layer used for detecting threats.
i). User Input Layer: This layer captures incoming text messages either through manual entry or automated SMS gateway integration. It acts as the first point of contact for the scam detection system.
ii). Preprocessing Layer: The raw message is cleaned to remove noise (e.g., symbols, slang), normalized, tokenized, and converted into a structured format suitable for feature extraction and analysis.
iii). Model Inference Layer: This is the system’s core analytical unit, where a trained machine learning model (e.g., Swahili-BERT) processes the preprocessed input and classifies it as either "Scam" or "Not Scam".
iv). Alert & Reporting Layer: What appears to be the function of this component, based on the classification, seems to be that the system either alerts the user, logs the message for monitoring, or tends to trigger what might be characterized as an optional response such as reporting the sender to authorities.
 
Figure 7.1: System Architecture Diagram (Placeholder)


6.3 Technology Stack
What seems especially noteworthy in this analytical context is that, to ensure flexibility, scalability, and cross-platform compatibility, the system was apparently built using modern, lightweight, and widely adopted technologies across the frontend, backend, model deployment, database, and hosting layers.
	Frontend: ReactJS / Flutter (for Android app): What the evidence appears to reveal is that ReactJS tends to be used to build responsive web interfaces, while Flutter seemingly provides a rich native-like experience for Android users, which appears to enable real-time scam detection from most devices, given the complexity of these theoretical relationships.
	Backend: Flask / FastAPI: Within this broader analytical framework, The study appears to represent what might be characterized as a Python web framework that seems to be suitable for what tends to suggest lightweight APIs, while FastAPI ostensibly offers what appears to be high performance and asynchronous request handling for what seems to constitute faster prediction response times in what appears to be typical production environments
	Model Deployment: TensorFlow Serving / ONNX / HuggingFace Transformers: What these findings seem to point toward is that these tools tend to enable seamless deployment of trained models. TensorFlow Serving and ONNX  support standard ML models, while HuggingFace Transformers are  used for Swahili BERT and other NLP specific architectures, considering the nature of these findings.
	Database: SQLite or PostgreSQL: What appears to emerge from these findings is that SQLite tends to be ideal for lightweight local storage during testing, while PostgreSQL is predominantly used for what appears to be scalable, secure storage of message logs and prediction metadata in the majority of production environments
	Hosting: AWS EC2 or Heroku: AWS EC2 provides customizable virtual servers for scalable hosting, while Heroku offers a simplified platform-as-a-service (PaaS) approach for rapid deployment and testing of the scam detection system.
6.4 Backend System Code (Flask API)
 

6.5 Sample User Interface (Mobile App)
The mobile application's interface appears to be designed with what seems to be simplicity, intuitiveness, and accessibility in mind for what tends to represent both technical and non-technical users. the design aims to enable is what could be described as real time scam message detection alongside to be seamless user reporting functionality.
i) Input Box: What seems to be implemented is a text field where users can enter or paste the content of what appears to be suspicious SMS messages for what might be characterized as analytical processing.
ii) Output: What tends to follow from this analysis, after what appears to be processing, is that the system seemingly displays whether the message is identified as a Scam or not Scam based on what the model’s prediction appears to suggest.
iii) Report Button: If the message is classified as a scam, users can instantly report it to the Tanzania Police Force database by clicking this button, aiding law enforcement monitoring.






Figure 7.2: Sample Mobile App UI Screenshot (Placeholder)
6.6 Real-Time Deployment Strategy
To ensure scalability and impact, the developed model will be integrated into real-world platforms using a multi-layered deployment approach that ensures automation, accessibility, and actionable insights for key stakeholders.
i) Integration with SMS Gateways: What appears to be particularly significant is the system's capacity to automatically scan incoming SMS in real-time, which seems to generally indicate a mechanism for flagging what might be characterized as suspicious content before it reaches users, apparently enhancing protection against what tends to represent mobile-based scams
ii) Cloud-hosted API for Telecoms and Cyber security Agencies: What the evidence appears to reveal is a framework that provides what seems to constitute remote access to detection services via secure endpoints, which tends to suggest what appears to be relatively straightforward integration into existing telecom and government systems.
iii) Dashboard for Law Enforcement: Within this broader analytical framework, the system ostensibly visualizes flagged messages, trends, and patterns, which appears to lend support to what may represent timely interventions, investigations, and decision-making by cybercrime and police units in the majority of cases.
6.7 Security and Privacy Considerations
What seems to emerge from these findings, given the complexity of these theoretical relationships, is that to ensure what appears to be ethical and secure deployment, the system seemingly incorporates what tends to be characterized as strong privacy-preserving measures including data encryption, role-based access control, and anonymization of user data to presumably prevent unauthorized access or misuse.
	Data Encryption: SSL/TLS for all API communications: the analysis support is that all data exchanged between clients and servers is  encrypted using SSL/TLS protocols, which appears to provide evidence that may support secure transmission and  prevents interception or tampering by  malicious actors.
	Access Control: Role-based user authentication: Considering the nuanced nature of these findings, system access is predominantly restricted through what appears to represent role-based user authentication, where permissions are apparently assigned based on user roles to seemingly prevent unauthorized data access and system misuse.
	Data Anonymization: No user-identifying metadata stored: What these findings seem to point toward is that all collected messages are substantially anonymized by stripping personally identifiable information, which tends to indicate what appears to be protection of user privacy and compliance with data protection regulations within these evolving conceptual parameters.
6.8 Usability Testing and Feedback
The study appears to have been conducted with diverse stakeholders to evaluate what seems to be the system's usability, user-friendliness, and detection accuracy in real-world scenarios, particularly within what might be characterized as Tanzanian law enforcement and cybersecurity environments.
i). Police Officers: The stakeholders appear to have done is test the system on settings to assess real time detection capabilities and its potential to support what tends to be characterized as investigations and digital evidence collection efforts, given the complexity of these theoretical relationships.
ii). Cybersecurity Analysts: What this group seemingly engaged in was an evaluation of model behavior, accuracy, and what appears to represent integration with existing cybersecurity tools for detecting and analyzing what might be considered text-based scam threats, within this broader analytical framework.
iii). Non-technical Personnel: What appears particularly significant about this stakeholder group is their focus on what seems to constitute interface usability, ease of navigation, and what tends to suggest an ability to understand scam classifications without what appears to be a technical background or ML knowledge, considering the nuanced nature of these findings
The evidence appears to reveal is that stakeholders generally appreciated what seems to be the real-time response, user-friendly design, and what appears to be substantial accuracy in identifying scam messages within what tends to be characterized as Swahili-language communications, in light of these methodological considerations.
 
Figure 7.3: Pilot User Feedback Chart (Placeholder)



CHAPTER SEVEN: 
DISCUSSION OF FINDINGS
7.1 Introduction
This chapter explores what appears to be the major findings from both the quantitative and qualitative data gathered during the study. It tends to evaluate what seems to be the effectiveness of machine learning models in detecting Kiswahili scam messages, aligns these findings with existing literature, and reflects on what may represent their implications for cybersecurity, language processing, and law enforcement in Tanzania.
7.2 Discussion Based on Research Objectives
7.2.1 Linguistic Characteristics of Kiswahili Scam Messages
The study appears to suggest is five dominant features in scam messages: requests for personal information, urgency or threats, poor grammar, reward promises, and emotional/religious appeals. These linguistic cues seem to be largely consistent with global scam detection patterns but appear to carry context-specific Swahili expressions and informal slang. What seems in this analytical context is how this aligns with previous studies, such as [7], which highlight what might be characterized as the role of colloquial Swahili and SMS language in phishing attacks.
7.2.2 Model Development and Effectiveness
The majority of tested models, Swahili BERT   outperformed traditional ML algorithms (SVM, RF) and Bi-LSTM in accuracy (96.3%), precision (96.5%), and F1-score (96.2%). What this pattern seems to suggest, therefore, is the significance of using transformer-based models that capture contextual language semantics. Given the complexity of these theoretical relationships, Bi-LSTM also showed substantial effectiveness due to what appears to be its capability to handle sequential data—further seeming to validate deep learning's suitability for natural language classification.
These findings seem to point toward mirrors patterns from recent research (e.g., Jiang, 2024; Xu et al., 2024), which appears to provide evidence that may support the superiority of contextual models like BERT in language-specific classification tasks
7.2.3 Stakeholder Perceptions and Real-World Application
The data seems to suggest is that the majority of respondents (84.6%) believed ML-based models can typically detect scam messages. Police officers, IT professionals, and cybersecurity analysts apparently supported the model's integration into law enforcement workflows. What appears particularly significant about these findings is that stakeholders emphasized key deployment concerns, including data privacy, system reliability, and usability for non-technical personnel. Within this broader analytical framework, this practical feedback tends to suggest what appears to be the importance of user-centric design in technological deployments, as also noted by [10], who presumably emphasizes the need for explainable and accessible AI in cybersecurity.
7.2.4 Dataset Contribution
The construction of a labeled Kiswahili scam message dataset appears to represent what might be characterized as a substantial contribution to NLP for low-resource languages. What the dataset seems to enable is further research, model benchmarking, and what appears to be real-world implementation. This tends to address what appears to be the research gap in annotated Swahili data as highlighted in earlier literature.

7.3 Alignment with Theoretical Framework
What the results appear to suggest, therefore, is support for the study's theoretical foundation in:
Information Processing Theory: What seems especially noteworthy is how scam messages apparently exploit psychological biases through urgency and emotional manipulation.
Communication Theory: What the evidence appears to reveal is that language in scam messages tends to reflect what seems to be deliberate persuasive and deceptive strategies.
Supervised Learning Theory: What appears to be demonstrated is that labeled data and supervised models seem to generally indicate high performance in classifying textual scams. 
CHAPTER EIGHT: 
SUMMARY, CONCLUSION, AND RECOMMENDATIONS
8.1 Summary of the Study
This research aimed to develop NLP-driven ML models to detect what appears to constitute scam messages written in Kiswahili. Given the complexity of these theoretical relationships, the study explored linguistic patterns in scam texts, constructed a labeled dataset, trained multiple models (including SVM, Random Forest, Bi-LSTM, and Swahili-BERT), and evaluated them using standard performance metrics. What also appears significant in this context is that the study examined the perception and expectations of stakeholders in Tanzania, particularly within the Tanzania Police Force. Key findings include:
	Swahili-BERT provided the highest detection accuracy.
	Mobile money fraud and lottery scams were ostensibly the most reported types.
	The study appears particularly significant about these findings is that urgency, personal info requests, and poor grammar were prevalent in the majority of scam messages.
	findings seem to point toward is that stakeholders showed substantial interest in ML solutions but emphasized what appears to be the need for real-time, user-friendly, and privacy-compliant deployment
8.2 Conclusion
What the analysis tends to support is that NLP-integrated ML models—especially transformer-based models like Swahili-BERT—appear to be effective tools for detecting Kiswahili scam messages. Within this broader analytical framework, the developed solution seems to provide what might be characterized as a significant technological advancement for cybersecurity in Tanzania, particularly for the Tanzania Police Force. In light of these methodological considerations, the research also appears to highlight the need for more Swahili-specific NLP resources and practical implementation strategies that address user experience, privacy, and scalability.
8.3 Recommendations
8.3.1 Policy and Law Enforcement
	Integrate the developed model into law enforcement systems to assist in scam message filtering, investigation, and digital forensics.
	Develop national frameworks and policies that support the ethical use of AI for public safety.
8.3.2 Technical Implementation
	Deploy the Swahili-BERT model in SMS gateway systems and mobile applications used by telecom providers and banks.
	Ensure data encryption and access control mechanisms are in place to protect user privacy.
8.3.3 Future Research
	Extend the model to detect multimodal scams (voice, email, and social media).
	Develop more Swahili-specific tools including named entity recognition (NER), spell checkers, and sentiment analyzers.
	Explore hybrid approaches that combine rule-based logic with ML to enhance accuracy.
 
REFERENCES
[1]. Jonathan, F., Yang, D., Gowing, G., & Wei, S. (2021). Machine Learning Framework for Detecting Offensive Swahili Messages in Social Networks with Apache Spark Implementation. Proceedings of the 2021 IEEE International Conference on Progress in Informatics and Computing, PIC 2021, December, 293–297. https://doi.org/10.1109/PIC53636.2021.9687001
[2]. Mambina, I. S., Ndibwile, J. D., & Michael, K. F. (2022). Classifying Swahili Smishing Attacks for Mobile Money Users: A Machine-Learning Approach. IEEE Access, 10(January 2022), 83061–83074. https://doi.org/10.1109/ACCESS.2022.3196464
[3]. Abbas, S. G., Vaccari, I., Hussain, F., Zahid, S., Fayyaz, U. U., Shah, G. A., Bakhshi, T., & Cambiaso, E. (2021). Identifying and mitigating phishing attack threats in IoT use cases using a threat modelling approach. Sensors, 21(14), 1–25. https://doi.org/10.3390/s2114481
[4]. Nakano, H., Koide, T., & Chiba, D. (2025). ScamFerret: Detecting Scam Websites Autonomously with Large Language Models. arXiv preprint arXiv:2502.10110. 
[5]. Ashraf, N., Mahmood, D., Obaidat, M. A., Ahmed, G., & Akhunzada, A. (2022). Criminal Behavior Identification Using Social Media Forensics. Electronics (Switzerland), 11(19). https://doi.org/10.3390/electronics11193162
[6]. Taherdoost, H. (2024). Insights into Cybercrime Detection and Response: A Review of Time Factor. Information, 15(5), 273. https://doi.org/10.3390/info15050273
[6]. Abbas, S. G., Vaccari, I., Hussain, F., Zahid, S., Fayyaz, U. U., Shah, G. A., Bakhshi, T., & Cambiaso, E. (2021). Identifying and mitigating phishing attack threats in IoT use cases using a threat modelling approach. Sensors, 21(14), 1–25. https://doi.org/10.3390/s21144816
[7]. Mambina, I. S., Ndibwile, J. D., & Michael, K. F. (2022). Classifying Swahili Smishing Attacks for Mobile Money Users: A Machine-Learning Approach
[8]. Jiang, L. (2024). Detecting Scams Using Large Language Models. arXiv preprint arXiv:2402.03147. 
[9]. Xu, H., Wang, S., Li, N., et al. (2024). Large Language Models for Cyber Security: A Systematic Literature Review. arXiv preprint arXiv:2405.04760. 
[10]. Ismail, W. S. (2024). Threat Detection and Response Using AI and NLP in Cybersecurity. Journal of Internet Services and Information Security, 14(1), 195-205. 
[11]. Atawneh, S., & Aljehani, A. (2024). Advancing Cybersecurity: A Comprehensive Review of AI-Driven Detection Techniques. Journal of Big Data, 11, Article 957. 
[12]. Rjoub, G., Bentahar, J., Abdel Wahab, O., et al. (2023). A Survey on Explainable Artificial Intelligence for Cybersecurity. arXiv preprint arXiv:2303.12942. 
[13]. Taherdoost, H. (2024). Insights into Cybercrime Detection and Response: A Review of Time Factor. Information, 15(5), 273.
[14] Mambina, I. S., Ndibwile, J. D., & Michael, K. F. (2022). Classifying Swahili Smishing Attacks for Mobile Money Users: A Machine-Learning Approach. IEEE Access, 10, 83062–83074 








APPENDEX


 
APPENDIX I: RESEARCH TIME FRAME
Table 8:Research Time Frame
	NO	Event/ Activity	February	March	April	May	June
		Weeks	Weeks	Weeks	Weeks	Weeks
	       	1	2	3	4	1	2	3	4	1	2	3	4	1	2	3	4	1	2	3	4
1	Literature Review																				
2	Data Collection																				
3	Data Analysis 																				
4	Development of electronic Machine Learning Swahili Scam  Messages 																				
5	Report writing																				
9	Submission of Research Report																				
 
APPENDEX II: RESEARCH BUDGET 
Table 9; Research Budget
No.	Expense Description	Amount (TZS)	Additional Notes
1.	Cloud Server and Data Storage	  1,500,000	For storing and processing data for ML training
2.	Swahili Scam Messages Dataset Acquisition	 1,000,000	If data is not freely available
3	Machine Learning Model Development	  2,000,000	Expert consultation and testing costs
4	API Fees and NLP Tools (Google Cloud, AWS, etc)	 1,200,000	For natural language processing (NLP)
5	Research and Data Analysis Costs	1,000,000	Data collection, cleaning, and analysis
6	User Interface (UI) Development	2,500,000	For a webapp dashboard
7	system Optimization and Verification (Testing & Debugging)	1,500,000	Ensuring the system functions correctly
8	Training and Workshop Costs for Users	1,500,000	Awareness and education sessions
9	Marketing and Community Engagement	800,000	Promotion and awareness campaigns
10	Project Management and Logistics	1,000,000	Meetings, reports, and administrative expenses

 
APPENDEX III: RESEARCH QUESTIONNAIRE
Title:Natural Language Processing Based Approach for Detecting Swahili Scam Messages Through Machine Learning Model in Tanzania: A Case Study of Tanzania Police Force
Introduction:
Dear respondent,
This questionnaire is part of a research study aimed at developing a machine learning-based model for detecting scam messages written in Kiswahili. Your responses is help in identifying linguistic patterns, evaluating classification techniques, and assessing deployment feasibility in real-world cybersecurity environments, particularly in law enforcement. All responses are kept strictly confidential and used for academic purposes only.
________________________________________
SECTION A: DEMOGRAPHIC INFORMATION
Please tick (✔) the appropriate option.
	Gender:
☐ Male
☐ Female
☐ Prefer not to say
	Age Group:
☐ 18 – 25
☐ 26 – 35
☐ 36 – 45
☐ 46 and above
	Occupation:
☐ Police Officer
☐ Cybersecurity Analyst
☐ IT Expert
☐ Academic/Researcher
☐ Other (please specify): _______________________
	Experience in handling cybercrime or scam messages:
☐ None
☐ Less than 1 year
☐ 1–3 years
☐ More than 3 years
________________________________________
SECTION B: UNDERSTANDING SCAM MESSAGES IN KISWAHILI
	In your experience, what types of scam messages are commonly reported in Kiswahili? (You may choose more than one)
☐ Lottery or prize scams
☐ Fake job offers
☐ Mobile money fraud
☐ Romance/relationship scams
☐ Impersonation (e.g., using police or government authority)
☐ Other (please specify): _______________________
	What are common features you observe in Kiswahili scam messages? (Tick all that apply)
☐ Poor grammar or misspellings
☐ Urgency or threats
☐ Requests for personal or financial information
☐ Promises of rewards or gifts
☐ Use of religious or emotional appeals
☐ Other (please specify): _______________________
	How frequently do you encounter Kiswahili scam messages?
☐ Daily
☐ Weekly
☐ Monthly
☐ Rarely
☐ Never
	In your opinion, what are the biggest challenges in detecting scam messages written in Kiswahili?
________________________________________
________________________________________
SECTION C: APPLICATION OF MACHINE LEARNING
	Do you believe machine learning can help in detecting Kiswahili scam messages?
☐ Yes
☐ No
☐ Not Sure
	What machine learning techniques do you believe are best suited for classifying scam messages?
☐ Naïve Bayes
☐ Support Vector Machine (SVM)
☐ Random Forest
☐ Deep Learning (e.g., LSTM, CNN)
☐ Not familiar with ML techniques
☐ Other (please specify): _______________________
	What features should be considered when training an ML model to detect scam messages in Kiswahili?
☐ Keywords/phrases
☐ Sender behavior
☐ Message structure
☐ Frequency of message patterns
☐ Other (please specify): _______________________
________________________________________
SECTION D: MODEL PERFORMANCE & DEPLOYMENT
	Which performance metric do you consider most important when evaluating an ML model for detecting scams?
☐ Accuracy
☐ Precision
☐ Recall
☐ F1-Score
☐ Speed of detection
☐ Other: _______________________
	Would you support integrating an automated scam detection system into Tanzania Police Force’s cybersecurity operations?
☐ Strongly Agree
☐ Agree
☐ Neutral
☐ Disagree
☐ Strongly Disagree
	In your view, what are the critical factors to consider when deploying such a model in a real-world system?
☐ User privacy and data protection
☐ Accuracy and reliability
☐ Ease of use for police and staff
☐ Real-time detection capabilities
☐ Cost and infrastructure
☐ Other: _______________________
	Please provide any additional comments or suggestions that can help improve scam detection using technology in Tanzania:
________________________________________
________________________________________
Thank you for your participation!
