# src/bilstm_model.py
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import pickle
import re
from collections import Counter

class SwahiliTextDataset(Dataset):
    def __init__(self, texts, labels, vocab, max_length=100):
        self.texts = texts
        self.labels = labels
        self.vocab = vocab
        self.max_length = max_length
        
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx]
        sequence = self.text_to_sequence(text)
        return torch.tensor(sequence, dtype=torch.long), torch.tensor(label, dtype=torch.long)
    
    def text_to_sequence(self, text):
        text = text.lower()
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        words = text.split()
        
        sequence = []
        for word in words[:self.max_length]:
            if word in self.vocab:
                sequence.append(self.vocab[word])
            else:
                sequence.append(self.vocab['<UNK>'])
        
        while len(sequence) < self.max_length:
            sequence.append(self.vocab['<PAD>'])
            
        return sequence[:self.max_length]

class BiLSTMClassifier(nn.Module):
    def __init__(self, vocab_size, embedding_dim=100, hidden_dim=128, num_layers=2, dropout=0.3):
        super(BiLSTMClassifier, self).__init__()
        
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx=0)
        self.lstm = nn.LSTM(
            embedding_dim, 
            hidden_dim, 
            num_layers, 
            batch_first=True, 
            bidirectional=True,
            dropout=dropout if num_layers > 1 else 0
        )
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_dim * 2, 2)
        
    def forward(self, x):
        embedded = self.embedding(x)
        lstm_out, (hidden, cell) = self.lstm(embedded)
        output = lstm_out[:, -1, :]
        output = self.dropout(output)
        output = self.fc(output)
        return output

class SwahiliBiLSTMTrainer:
    def __init__(self, data_path='data/raw/swahili_messages_sample.csv'):
        self.data_path = data_path
        self.vocab = {}
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔥 Using device: {self.device}")
        
    def load_and_preprocess_data(self):
        print("📊 Loading dataset...")
        df = pd.read_csv(self.data_path)
        print(f"✅ Loaded {len(df)} messages")
        texts = df['text'].tolist()
        labels = df['label'].tolist()
        return texts, labels
    
    def build_vocabulary(self, texts, min_freq=2):
        print("🔤 Building vocabulary...")
        word_counts = Counter()
        for text in texts:
            text = text.lower()
            text = re.sub(r'[^a-zA-Z\s]', '', text)
            words = text.split()
            word_counts.update(words)
        
        vocab = {'<PAD>': 0, '<UNK>': 1}
        idx = 2
        
        for word, count in word_counts.items():
            if count >= min_freq:
                vocab[word] = idx
                idx += 1
        
        print(f"✅ Vocabulary size: {len(vocab)}")
        self.vocab = vocab
        return vocab
    
    def train_model(self, texts, labels, epochs=20, batch_size=32):
        print("🚀 Starting Bi-LSTM training...")
        
        self.build_vocabulary(texts)
        
        X_train, X_test, y_train, y_test = train_test_split(
            texts, labels, test_size=0.2, random_state=42, stratify=labels
        )
        
        train_dataset = SwahiliTextDataset(X_train, y_train, self.vocab)
        test_dataset = SwahiliTextDataset(X_test, y_test, self.vocab)
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        vocab_size = len(self.vocab)
        self.model = BiLSTMClassifier(vocab_size).to(self.device)
        
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(self.model.parameters(), lr=0.001)
        
        for epoch in range(epochs):
            self.model.train()
            total_loss = 0
            correct = 0
            total = 0
            
            for batch_texts, batch_labels in train_loader:
                batch_texts = batch_texts.to(self.device)
                batch_labels = batch_labels.to(self.device)
                
                outputs = self.model(batch_texts)
                loss = criterion(outputs, batch_labels)
                
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total += batch_labels.size(0)
                correct += (predicted == batch_labels).sum().item()
            
            if (epoch + 1) % 5 == 0:
                epoch_acc = 100 * correct / total
                print(f"Epoch [{epoch+1}/{epochs}] - Accuracy: {epoch_acc:.2f}%")
        
        test_accuracy, test_precision, test_recall, test_f1 = self.evaluate_model(test_loader)
        
        print("\n🎯 FINAL BI-LSTM RESULTS:")
        print("=" * 50)
        print(f"📊 Test Accuracy:  {test_accuracy:.2f}%")
        print(f"📊 Test Precision: {test_precision:.2f}%")
        print(f"📊 Test Recall:    {test_recall:.2f}%")
        print(f"📊 Test F1-Score:  {test_f1:.2f}%")
        
        self.save_model()
        
        return {
            'accuracy': test_accuracy,
            'precision': test_precision,
            'recall': test_recall,
            'f1_score': test_f1
        }
    
    def evaluate_model(self, test_loader):
        self.model.eval()
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch_texts, batch_labels in test_loader:
                batch_texts = batch_texts.to(self.device)
                batch_labels = batch_labels.to(self.device)
                
                outputs = self.model(batch_texts)
                _, predicted = torch.max(outputs, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(batch_labels.cpu().numpy())
        
        accuracy = accuracy_score(all_labels, all_predictions) * 100
        precision = precision_score(all_labels, all_predictions) * 100
        recall = recall_score(all_labels, all_predictions) * 100
        f1 = f1_score(all_labels, all_predictions) * 100
        
        return accuracy, precision, recall, f1
    
    def save_model(self):
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'vocab': self.vocab
        }, 'models/bilstm_model.pth')
        
        with open('models/bilstm_vocab.pkl', 'wb') as f:
            pickle.dump(self.vocab, f)
        
        print("✅ Bi-LSTM model saved to models/bilstm_model.pth")

def main():
    print("🚀 SWAHILI BI-LSTM SCAM DETECTOR")
    print("=" * 50)
    
    trainer = SwahiliBiLSTMTrainer()
    texts, labels = trainer.load_and_preprocess_data()
    results = trainer.train_model(texts, labels, epochs=25)

if __name__ == "__main__":
    main()