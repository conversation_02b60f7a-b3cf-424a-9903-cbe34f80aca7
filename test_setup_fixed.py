# test_setup_fixed.py
print("Testing Python libraries (TensorFlow alternative)...")

try:
    import pandas as pd
    print("✅ pandas installed")
except ImportError:
    print("❌ pandas not installed")

try:
    import numpy as np
    print("✅ numpy installed")
except ImportError:
    print("❌ numpy not installed")

try:
    import sklearn
    print("✅ scikit-learn installed")
except ImportError:
    print("❌ scikit-learn not installed")

try:
    import torch
    print("✅ PyTorch installed (TensorFlow alternative)")
except ImportError:
    print("❌ PyTorch not installed")

try:
    import transformers
    print("✅ transformers installed")
except ImportError:
    print("❌ transformers not installed")

try:
    import flask
    print("✅ flask installed")
except ImportError:
    print("❌ flask not installed")

print(f"\n🐍 Python version: {__import__('sys').version}")
print("\n🎯 Setup test complete!")
print("✅ Ready to proceed with PyTorch instead of TensorFlow!")