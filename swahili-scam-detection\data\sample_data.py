# data/sample_data.py
import pandas as pd
import json
from datetime import datetime

def create_sample_dataset():
    """Create sample Swahili scam and legitimate messages"""
    
    # Sample scam messages (label = 1)
    scam_messages = [
        "Hongera! Umeshinda TSH 5,000,000 katika mchezo wa bahati nasibu. Piga *150*00# sasa kuconfirm.",
        "URGENT! Mama yako amelazwa hospitalini. Tuma pesa haraka kwa namba hii **********.",
        "Congratulations! You have won $10,000 USD. Send your details to claim prize now!",
        "Bahati nasibu! Umeshinda gari jipya. <PERSON><PERSON> ya TSH 50,000 tu kuconfirm. Haraka!",
        "FREE MONEY! Pokea TSH 2,000,000 bure. Piga simu hii ********** sasa hivi.",
        "Zawadi kubwa! Bank ya Tanzania inakupa TSH 1,000,000. Tuma jina lako na namba ya simu.",
        "WINNER! Umeshinda jackpot ya TSH 10,000,000. <PERSON><PERSON> mdogo wa TSH 100,000 kuconfirm.",
        "Haraka! Pesa zako TSH 3,000,000 zinasubiri. Piga *123# kuconfirm kabla ya saa 6.",
        "Hongera mshindi! Lottery ya kimataifa. Tuma copy ya kitambulisho kuconfirm ushindi wako.",
        "URGENT CASH! Pokea TSH 500,000 bila riba. Piga ********** sasa hivi.",
        "Bahati yako leo! Umeshinda TSH 2,500,000. Malipo ya activation TSH 25,000 tu.",
        "FREE GIFT! Simu mpya iPhone 15. Tuma TSH 80,000 kwa delivery na taxes.",
        "Mchezo wa bahati! Bank inakupa loan ya TSH 5,000,000 bila dhamana. Piga sasa!",
        "WINNER ALERT! Umeshinda TSH 8,000,000. Processing fee TSH 150,000 tu.",
        "Zawadi ya Mungu! Pokea TSH 1,500,000 kutoka kwa mzungu mzuri. Tuma details zako.",
        "Haraka kabla haijachelewa! TSH 4,000,000 zinasubiri. Malipo ya TSH 75,000 kuconfirm.",
        "JACKPOT! Umeshinda TSH 15,000,000. Tuma photocopy ya kitambulisho na TSH 200,000.",
        "Bahati nasibu ya ajabu! Pokea TSH 3,500,000 bure. Piga *789# kuconfirm sasa.",
        "FREE MONEY ALERT! TSH 2,200,000 kutoka serikali. Tuma jina na namba ya akaunti.",
        "URGENT! Baba yako amefariki. Tuma TSH 300,000 kwa mazishi haraka **********."
    ]
    
    # Sample legitimate messages (label = 0)
    legitimate_messages = [
        "Habari za asubuhi. Mkutano wetu ni saa 2 jioni leo katika ofisi.",
        "Asante kwa huduma nzuri. Tuonane kesho asubuhi saa 9.",
        "Hujambo? Naomba unipigie simu ukipata nafasi.",
        "Shule itafungwa Ijumaa hii kwa sherehe za kitaifa.",
        "Duka letu litafunguka saa 8 asubuhi na kufunga saa 6 jioni.",
        "Hongera kwa kazi nzuri uliyofanya katika mradi huu.",
        "Naomba uniletee vitabu vile ukija kazini kesho.",
        "Chakula kiko tayari. Karibu nyumbani ukimaliza kazi.",
        "Gari limeharibika. Nitachelewa kidogo kufika kazini.",
        "Asante kwa zawadi nzuri ya siku yangu ya kuzaliwa.",
        "Mkutano umehairishwa hadi wiki ijayo Jumatatu.",
        "Naomba unisaidie kwa kazi ya nyumbani ya hisabati.",
        "Daktari amesema uende hospitali kesho kwa uchunguzi.",
        "Familia yetu itakutana Jumapili kwa chakula cha mchana.",
        "Shule imeanza likizo. Watoto watarudi tarehe 15.",
        "Naomba unipe namba ya simu ya daktari wako.",
        "Tuende sokoni pamoja asubuhi ya Jumamosi.",
        "Barua yako imefika. Nitakupatia ukija ofisini.",
        "Mti umeanguka barabarani. Barabara imefungwa kwa muda.",
        "Umeme utakatika saa 2 hadi saa 6 kwa matengenezo."
    ]
    
    # Create DataFrame
    data = []
    
    # Add scam messages
    for i, message in enumerate(scam_messages):
        data.append({
            'message_id': f'scam_{i+1:03d}',
            'text': message,
            'label': 1,
            'category': 'scam',
            'source': 'manual_collection',
            'date_collected': datetime.now().strftime('%Y-%m-%d'),
            'language': 'swahili'
        })
    
    # Add legitimate messages
    for i, message in enumerate(legitimate_messages):
        data.append({
            'message_id': f'legit_{i+1:03d}',
            'text': message,
            'label': 0,
            'category': 'legitimate',
            'source': 'manual_collection',
            'date_collected': datetime.now().strftime('%Y-%m-%d'),
            'language': 'swahili'
        })
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Shuffle the data
    df = df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    return df

def save_dataset(df, filename='swahili_messages_sample.csv'):
    """Save dataset to CSV file"""
    filepath = f'data/raw/{filename}'
    df.to_csv(filepath, index=False, encoding='utf-8')
    print(f"✅ Dataset saved to: {filepath}")
    return filepath

def analyze_dataset(df):
    """Analyze the created dataset"""
    print("📊 DATASET ANALYSIS")
    print("=" * 40)
    print(f"Total messages: {len(df)}")
    print(f"Scam messages: {len(df[df['label'] == 1])}")
    print(f"Legitimate messages: {len(df[df['label'] == 0])}")
    print(f"Scam ratio: {df['label'].mean():.1%}")
    
    print("\n📝 SAMPLE MESSAGES:")
    print("\n🚨 Scam Examples:")
    scam_samples = df[df['label'] == 1]['text'].head(3)
    for i, msg in enumerate(scam_samples, 1):
        print(f"{i}. {msg}")
    
    print("\n✅ Legitimate Examples:")
    legit_samples = df[df['label'] == 0]['text'].head(3)
    for i, msg in enumerate(legit_samples, 1):
        print(f"{i}. {msg}")
    
    print("\n📈 TEXT STATISTICS:")
    print(f"Average message length: {df['text'].str.len().mean():.1f} characters")
    print(f"Average word count: {df['text'].str.split().str.len().mean():.1f} words")

if __name__ == "__main__":
    print("🏗️ Creating Swahili Sample Dataset...")
    print("=" * 50)
    
    # Create dataset
    df = create_sample_dataset()
    
    # Analyze dataset
    analyze_dataset(df)
    
    # Save dataset
    filepath = save_dataset(df)
    
    print(f"\n✅ Sample dataset created successfully!")
    print(f"📁 File location: {filepath}")
    print(f"📊 Ready for preprocessing and model training!")