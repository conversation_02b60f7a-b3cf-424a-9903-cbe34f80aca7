{"best_global_step": 53, "best_metric": 0.18761244416236877, "best_model_checkpoint": "./models/bert_results\\checkpoint-53", "epoch": 1.0, "eval_steps": 500, "global_step": 53, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.0, "eval_accuracy": 1.0, "eval_f1": 1.0, "eval_loss": 0.18761244416236877, "eval_precision": 1.0, "eval_recall": 1.0, "eval_runtime": 49.4889, "eval_samples_per_second": 4.243, "eval_steps_per_second": 0.283, "step": 53}], "logging_steps": 500, "max_steps": 159, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 55121766097920.0, "train_batch_size": 16, "trial_name": null, "trial_params": null}