# src/model_comparison.py
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import joblib
import matplotlib.pyplot as plt
import seaborn as sns

# Import our custom models
from model_trainer import SwahiliScamModelTrainer
from bilstm_model import SwahiliBiLSTMTrainer
from bert_model import SwahiliBERTTrainer

class ModelComparison:
    def __init__(self, data_path='data/raw/swahili_messages_sample.csv'):
        self.data_path = data_path
        self.results = {}
        
    def load_data(self):
        print("📊 Loading dataset for comparison...")
        df = pd.read_csv(self.data_path)
        texts = df['text'].tolist()
        labels = df['label'].tolist()
        return texts, labels
    
    def run_all_models(self):
        print("🚀 RUNNING ALL MODELS FOR COMPARISON")
        print("=" * 60)
        
        texts, labels = self.load_data()
        
        # 1. Traditional ML Models
        print("\n1️⃣ TRADITIONAL ML MODELS")
        print("-" * 30)
        ml_trainer = SwahiliScamModelTrainer()
        ml_results = ml_trainer.train_and_evaluate()
        
        self.results['Random Forest'] = {
            'accuracy': ml_results['random_forest']['accuracy'],
            'precision': ml_results['random_forest']['precision'],
            'recall': ml_results['random_forest']['recall'],
            'f1_score': ml_results['random_forest']['f1_score']
        }
        
        self.results['SVM'] = {
            'accuracy': ml_results['svm']['accuracy'],
            'precision': ml_results['svm']['precision'],
            'recall': ml_results['svm']['recall'],
            'f1_score': ml_results['svm']['f1_score']
        }
        
        self.results['Logistic Regression'] = {
            'accuracy': ml_results['logistic_regression']['accuracy'],
            'precision': ml_results['logistic_regression']['precision'],
            'recall': ml_results['logistic_regression']['recall'],
            'f1_score': ml_results['logistic_regression']['f1_score']
        }
        
        # 2. Bi-LSTM Model
        print("\n2️⃣ BI-LSTM MODEL")
        print("-" * 30)
        bilstm_trainer = SwahiliBiLSTMTrainer()
        bilstm_results = bilstm_trainer.train_model(texts, labels)
        
        self.results['Bi-LSTM'] = bilstm_results
        
        # 3. BERT Model
        print("\n3️⃣ BERT MODEL")
        print("-" * 30)
        bert_trainer = SwahiliBERTTrainer()
        bert_results = bert_trainer.train_model(texts, labels)
        
        self.results['BERT'] = bert_results
        
        # Generate comparison report
        self.generate_comparison_report()
        self.plot_comparison()
        
    def generate_comparison_report(self):
        print("\n🎯 COMPREHENSIVE MODEL COMPARISON")
        print("=" * 60)
        
        # Create comparison DataFrame
        comparison_data = []
        for model_name, metrics in self.results.items():
            comparison_data.append({
                'Model': model_name,
                'Accuracy (%)': f"{metrics['accuracy']:.2f}",
                'Precision (%)': f"{metrics['precision']:.2f}",
                'Recall (%)': f"{metrics['recall']:.2f}",
                'F1-Score (%)': f"{metrics['f1_score']:.2f}"
            })
        
        df_comparison = pd.DataFrame(comparison_data)
        print(df_comparison.to_string(index=False))
        
        # Save to CSV
        df_comparison.to_csv('models/model_comparison_results.csv', index=False)
        print(f"\n✅ Results saved to: models/model_comparison_results.csv")
        
        # Find best model
        best_model = max(self.results.items(), key=lambda x: x[1]['accuracy'])
        print(f"\n🏆 BEST MODEL: {best_model[0]} with {best_model[1]['accuracy']:.2f}% accuracy")
        
        # Academic insights
        print(f"\n📚 ACADEMIC INSIGHTS:")
        print(f"📈 Accuracy Range: {min([r['accuracy'] for r in self.results.values()]):.1f}% - {max([r['accuracy'] for r in self.results.values()]):.1f}%")
        print(f"📈 Best F1-Score: {max([r['f1_score'] for r in self.results.values()]):.2f}%")
        
        if best_model[1]['accuracy'] >= 95:
            print("✅ RESEARCH TARGET ACHIEVED: >95% accuracy!")
        else:
            print("⚠️  Research target (95%+) not yet achieved")
    
    def plot_comparison(self):
        # Create visualization
        models = list(self.results.keys())
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Swahili Scam Detection - Model Comparison', fontsize=16)
        
        for i, metric in enumerate(metrics):
            ax = axes[i//2, i%2]
            values = [self.results[model][metric] for model in models]
            
            bars = ax.bar(models, values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'])
            ax.set_title(f'{metric.replace("_", " ").title()} (%)')
            ax.set_ylabel('Percentage')
            ax.set_ylim(0, 100)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                       f'{value:.1f}%', ha='center', va='bottom')
            
            # Rotate x-axis labels
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('models/model_comparison_chart.png', dpi=300, bbox_inches='tight')
        print("📊 Comparison chart saved to: models/model_comparison_chart.png")
        plt.show()

def main():
    print("🔬 SWAHILI SCAM DETECTION - MODEL COMPARISON STUDY")
    print("=" * 70)
    
    comparison = ModelComparison()
    comparison.run_all_models()

if __name__ == "__main__":
    main()